{"name": "speed-painter", "description": "speed-painter", "engines": {"node": "^18 || ^20.10.0", "npm": "^9 || ^10"}, "scripts": {"start": "ts-node ./scripts/start/start.ts", "extract": "formatjs extract 'src/**/*.{ts,tsx}' --out-file dist/messages_en.json", "build": "webpack --config webpack.config.js --mode production && npm run extract", "lint:types": "tsc", "lint": "eslint .", "lint:fix": "eslint . --fix", "format": "prettier '{examples,src,utils,scripts}/**/*.{css,ts,tsx,json,js}' --no-config --write", "format:check": "prettier '{examples,src,utils,scripts}/**/*.{css,ts,tsx,json,js}' --no-config --check --ignore-path", "format:file": "prettier $1 --no-config --write", "test": "jest --no-cache", "test:watch": "jest --no-cache --watch"}, "keywords": [], "author": "Canva Pty Ltd.", "license": "SEE LICENSE IN LICENSE.md", "private": true, "workspaces": ["./examples/*"], "dependencies": {"@canva/app-i18n-kit": "^1.0.2", "@canva/app-ui-kit": "^4.10.0", "@canva/asset": "^2.2.0", "@canva/design": "^2.4.1", "@canva/error": "^2.1.0", "@canva/platform": "^2.1.0", "@canva/preview": "./sdk/preview", "@canva/user": "^2.1.0", "@tanstack/react-query": "^5.80.3", "clsx": "^2.1.1", "fetch-event-stream": "^0.1.5", "react": "18.3.1", "react-dom": "18.3.1", "react-intl": "6.6.8", "sha256": "^0.2.0", "zustand": "^5.0.2"}, "devDependencies": {"@eslint/eslintrc": "3.1.0", "@eslint/js": "9.9.0", "@formatjs/cli": "6.2.12", "@formatjs/ts-transformer": "3.13.14", "@ngrok/ngrok": "1.4.1", "@svgr/webpack": "8.1.0", "@tanstack/react-query-devtools": "^5.80.3", "@testing-library/dom": "10.4.0", "@testing-library/react": "16.0.0", "@types/debug": "4.1.12", "@types/express": "4.17.21", "@types/jest": "29.5.12", "@types/jsonwebtoken": "9.0.6", "@types/node": "20.10.0", "@types/node-fetch": "2.6.11", "@types/node-forge": "1.3.11", "@types/nodemon": "1.19.6", "@types/prompts": "2.4.9", "@types/react": "18.3.4", "@types/react-dom": "18.3.0", "@types/webpack-env": "1.18.5", "@typescript-eslint/eslint-plugin": "8.2.0", "@typescript-eslint/parser": "8.2.0", "chalk": "4.1.2", "cli-table3": "0.6.5", "css-loader": "7.1.2", "css-modules-typescript-loader": "4.0.1", "cssnano": "7.0.5", "debug": "4.3.6", "dotenv": "16.4.5", "eslint": "8.57.1", "eslint-plugin-formatjs": "4.13.3", "eslint-plugin-jest": "28.8.0", "eslint-plugin-react": "7.35.0", "exponential-backoff": "3.1.1", "express": "4.21.0", "express-basic-auth": "1.2.1", "globals": "15.9.0", "jest": "29.7.0", "jest-css-modules-transform": "4.4.2", "jest-environment-jsdom": "29.7.0", "jsonwebtoken": "9.0.2", "jwks-rsa": "3.1.0", "mini-css-extract-plugin": "2.9.1", "node-fetch": "3.3.2", "node-forge": "1.3.1", "nodemon": "3.0.1", "postcss-loader": "8.1.1", "prettier": "3.3.3", "prompts": "2.4.2", "style-loader": "4.0.0", "terser-webpack-plugin": "5.3.10", "ts-jest": "29.2.4", "ts-loader": "9.5.1", "ts-node": "10.9.2", "typescript": "5.5.4", "url-loader": "4.1.1", "webpack": "5.94.0", "webpack-cli": "5.1.4", "webpack-dev-server": "5.0.4", "yargs": "17.7.2"}}