.draggable {
  cursor: pointer;
  display: inline-block;
}
/* List item */
.listItem {
  margin-bottom: var(--ui-kit-space-1);
}

/* Code */
.code {
  font-family: "Menlo";
}

/* Thumbnail */
.thumbnailGrid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--ui-kit-space-150);
  margin-bottom: var(--ui-kit-space-3);
  padding-left: 6px;
  padding-right: 6px;
}

.thumbnail {
  width: 100%;
  position: relative;
  border-radius: calc(var(--ui-kit-border-radius) / 2);
  cursor: pointer;
}

.thumbnail.active {
  outline: solid 2px var(--ui-kit-color-primary);
  outline-offset: var(--ui-kit-space-050);
}
.h100 {
  height: 100%;
}
/* Scroll container */
.scrollContainer {
  box-sizing: border-box;
  overflow-y: auto;
  height: 100%;
  padding-top: var(--ui-kit-space-1);
  padding-right: var(--ui-kit-space-2);
  padding-bottom: var(--ui-kit-space-1);
  display: flex;
  flex-direction: column;
  justify-content: space-between;

  /* for firefox */
  /* scrollbar-width: thin;
    scrollbar-color: var(--ui-kit-color-typography-quaternary) transparent; */
}

.resultscrollContainer {
  padding-top: var(--ui-kit-space-1);
  padding-right: var(--ui-kit-space-2);
  box-sizing: border-box;
}

/* .scrollContainer::-webkit-scrollbar {
    position: absolute;
    width: var(--ui-kit-base-unit);
    height: 0;
}

.scrollContainer::-webkit-scrollbar-track {
    background: transparent;
    width: var(--ui-kit-base-unit);
    margin-top: var(--ui-kit-space-1);
    margin-bottom: var(--ui-kit-space-1);
}

.scrollContainer::-webkit-scrollbar-thumb {
    border-radius: var(--ui-kit-border-radius);
    background: var(--ui-kit-color-typography-quaternary);
    visibility: hidden;
}

.scrollContainer:hover::-webkit-scrollbar-thumb,
.scrollContainer:focus::-webkit-scrollbar-thumb,
.scrollContainer:focus-within::-webkit-scrollbar-thumb {
    visibility: visible;
} */

div {
  box-sizing: border-box;
}

.waitingContainer {
  width: 100%;
  height: calc(100vh - 40px);
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
}

.progressBox {
  width: 100%;
  /* height: 20px; */
  /* border-radius: 10px;
  margin: 10px 0;
  overflow: hidden;
  background-color: rgba(255, 255, 255, 0.07); */
  margin-top: var(--ui-kit-space-1);
  margin-bottom: var(--ui-kit-space-1);
}
.progressBox .progress {
  height: 100%;
  border-radius: 10px;
  background-image: linear-gradient(to right, #9c74da, #4fb3cf);
  transition: width 1s linear;
}

.appContainer {
  height: 100%;
  display: flex;
  flex-direction: column;
}
