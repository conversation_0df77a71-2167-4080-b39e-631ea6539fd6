.waittingContainer {
  width: 100%;
  height: calc(100vh - 40px);
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  
}

.progressBox {
  width: 100%;
  /* height: 20px; */
  /* border-radius: 10px;
  margin: 10px 0;
  overflow: hidden;
  background-color: rgba(255, 255, 255, 0.07); */
  margin-top: var(--ui-kit-space-1);
  margin-bottom: var(--ui-kit-space-1);
}
.progressBox .progress {
  height: 100%;
  border-radius: 10px;
  background-image: linear-gradient(to right, #9C74DA, #4FB3CF);
  transition: width 1s linear;
}

.progressAnimation {
  height: 100%;
  width: 100%;
  transform-origin: left center;
  border-radius: 10px;
  background-image: linear-gradient(to right, #9C74DA, #4FB3CF);
  animation: progressAnimation 1s linear 0s infinite;
}

@keyframes progressAnimation {
  0% {
    transform: scaleX(0);
  }
  100% {
    transform: scaleY(1);
  }
}

.cancleBtn {
  margin-top: var(--ui-kit-space-3);
  width: 100%;
}