.title {
  margin-top: var(--ui-kit-space-1);
}

.resizeModeBox {
  display: flex;
  position: relative;
  width: 100%;
  height: 32px;
  background: #ffffff33;
  border-radius: 16px;
  color: #fff;
}
.resizeModeBoxV2 {
  width: 100%;
}

.resizeModeBox span {
  display: block;
  flex: 1;
  height: 32px;
  line-height: 32px;
  text-align: center;
  font-size: 14px;
  border-radius: 16px;
  cursor: pointer;
  position: relative;
  z-index: 10;
}
.resizeModeBox span.active {
  background-color: #8b3bfe;
  cursor: default;
}

.resizeModeBox span > div {
  display: none;
}

.resizeModeBox > div {
  position: absolute;
  z-index: 5;
  width: 25%;
  height: 32px;
  background-color: #8b3bfe;
  border-radius: 16px;
  top: 0;
  left: 0;
  transition: transform 0.15s linear;
  will-change: transform;
}
.resizeModeBox > div.active0 {
  transform: translateX(-100%);
}
.resizeModeBox > div.active1 {
  transform: translateX(0%);
}
.resizeModeBox > div.active2 {
  transform: translateX(100%);
}
.resizeModeBox > div.active3 {
  transform: translateX(200%);
}
.resizeModeBox > div.active4 {
  transform: translateX(300%);
}

.resizeModeDisable {
  color: #aaa;
  cursor: not-allowed !important;
}

.resizeModeDisable > div {
  position: absolute;
  display: block !important;
  width: 13px;
  height: 13px;
  top: 4px;
  right: 17px;
}
.resizeModeDisable > div img:first-child {
  width: 13px;
  height: 13px;
}
.resizeModeDisable > div img:last-child {
  height: 8px;
  position: absolute;
  top: -13px;
}
.resizeModeDisable > div p {
  position: absolute;
  width: 200px;
  height: 88px;
  padding: 14px;
  color: #333;
  background-color: #fff;
  bottom: 25px;
  left: -200px;
  text-align: left;
  font-size: 12px;
  border-radius: 10px;
}

.resizeModeDisableLeft {
  color: #aaa;
  cursor: not-allowed !important;
}

.resizeModeDisableLeft > div {
  position: absolute;
  display: block !important;
  width: 13px;
  height: 13px;
  top: 4px;
  right: 17px;
}
.resizeModeDisableLeft > div img:first-child {
  width: 13px;
  height: 13px;
}
.resizeModeDisableLeft > div img:last-child {
  height: 8px;
  position: absolute;
  top: -13px;
}
.resizeModeDisableLeft > div p {
  position: absolute;
  width: 200px;
  height: 88px;
  padding: 14px;
  color: #333;
  background-color: #fff;
  bottom: 25px;
  left: -50px;
  text-align: left;
  font-size: 12px;
  border-radius: 10px;
}

.resizeModeTitle {
  display: flex;
  align-items: center;
  margin-top: var(--ui-kit-space-2);
}

.resizeModeTitle > div {
  width: 13px;
  height: 13px;
  cursor: pointer;
  margin-left: 10px;
  position: relative;
  display: flex;
  z-index: 1000;
}
.resizeModeTitle > div img:first-child {
  width: 13px;
  height: 13px;
}
.resizeModeTitle > div img:last-child {
  height: 8px;
  position: absolute;
  top: -13px;
}
.resizeModeTitle > div p {
  position: absolute;
  width: 200px;
  height: 88px;
  padding: 14px;
  color: #333;
  background-color: #fff;
  bottom: 25px;
  left: -50px;
  text-align: left;
  font-size: 12px;
  border-radius: 10px;
}
.aiModelBox {
  margin-top: var(--ui-kit-space-2);
}
.aiModelList {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

.aiModelList > div {
  width: calc(50vw - 24px);
  height: calc(50vw - 24px);
  border-radius: 8px;
  background-color: #8b3bfe;
  margin-bottom: 16px;
  cursor: pointer;
  background-size: cover;
  position: relative;
  overflow: hidden;
}

.aiModelList > div img {
  width: 36px;
  height: 36px;
  position: absolute;
  top: 0;
  right: 0;
  display: none;
}
.aiModelList > div.active::after {
  content: "";
  display: block;
  border: 2px solid #8b3bfe;
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 8px;
  left: 0;
  top: 0;
  box-sizing: border-box;
}
.aiModelList > div.active img {
  display: block;
}
.aiModelList > div span {
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 34px;
  background-color: rgba(0, 0, 0, 0.3);
  color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 14px;
  font-weight: 500;
}

.btnBox {
  margin-top: var(--ui-kit-space-2);
  padding-bottom: var(--ui-kit-space-1);
}

/* .alert {
  background-color: var(--ui-kit-color-info-low);
  padding: 8px 12px;
  border-radius: 4px;
} */

.alert > p {
  display: inline;
}
.imgLoadingBox {
  width: 100%;
  height: 185px;
  border-radius: 8px;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  color: #fff;
  position: relative;
  overflow: hidden;
  /* background-color: var(--ui-kit-color-background-1); */
  /* background-color: rgba(255, 255 , 255, 0.07); */
  background-color: var(--ui-kit-color-neutral);
}
.dragUploadBox {
  width: 100%;
  height: auto;
  height: 185px;
  border-radius: var(--ui-kit-space-1);
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  color: #fff;
  position: relative;
  overflow: hidden;
  /* background-color: var(--ui-kit-color-background-1); */
  /* background-color: rgba(255, 255 , 255, 0.07); */
  background-color: var(--ui-kit-color-neutral);
  background-repeat: no-repeat;
  background-size: contain;
  background-position: center;
}
.userUploadImage {
  position: relative;
}
.userUploadImage .upImg {
  height: auto;
  width: auto;
  max-width: calc(100vw - 64px);
  max-height: 185px;
  pointer-events: none;
}
.userUploadImage .delete {
  width: 24px;
  height: 24px;
  position: absolute;
  top: -8px;
  right: -8px;
  cursor: pointer;
}

.uploadImage {
  width: 54px;
  height: 46px;
  position: relative;
  z-index: 5;
}

.video {
  max-width: 100%;
  max-height: 100%;
}
.videoInput {
  display: none;
}

.uploadVideoContainer {
  margin-bottom: var(--ui-kit-space-4);
  border: 2px dashed #e0e0e0;
  border-radius: 8px;
  padding: 32px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  min-height: 200px;
}

.uploadVideoContainer:hover {
  border-color: var(--ui-kit-color-primary-hover);
  background-color: #f5f5f5;
}

.uploadVideoContent {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  text-align: center;
}

.uploadIcon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background-color: #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
  color: #666;
}

.videoPreviewWrapper {
  padding: 0;
  margin: 0;
  border-radius: 8px;
  overflow: hidden;
}

.videoPreviewContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 8px;
  overflow: hidden;
  padding: 8px;
  height: 200px;
  width: 100%;
  position: relative;
}

.videoPreview {
  width: 100%;
  display: block;
}

.videoMetadata {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 12px;
  margin-bottom: 4px;
}

.videoFileInfo {
  display: flex;
  align-items: center;
  gap: 8px;
}

.trashButton {
  background: none;
  border: none;
  cursor: pointer;
  color: #666;
  padding: 4px;
}

.trashButton:hover {
  color: #ff3b30;
}

.removeButton {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: rgba(255, 0, 0, 0.7);
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.2s;
}

.removeButton:hover {
  background-color: rgba(255, 0, 0, 0.9);
}

.textCenter {
  text-align: center;
}

.simpleUploadContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  margin-bottom: 24px;
}
