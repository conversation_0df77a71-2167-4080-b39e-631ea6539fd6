/* .toastBox {
  position: fixed;
  z-index: 800;
  width: 300px;
  min-height: 50px;
  display: flex;
  border-radius: 25px;
  align-items: center;
  justify-content: center;
  background-color: #000;
  color: #fff;
  top: 30px;
  left: 0;
  right: 0;
  margin: auto;
  padding: 10px;
  text-align: center;
} */

.alert {
  background-color: var(--ui-kit-color-critical-low);
  padding: 8px 12px;
  border-radius: 4px;
  margin-top: var(--ui-kit-space-1);
  margin-bottom: var(--ui-kit-space-1);
}

.alert  >p{
  display: inline;
}