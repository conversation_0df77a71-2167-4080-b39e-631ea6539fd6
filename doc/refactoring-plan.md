# Canva VU 轻量级重构方案

## 1. 项目现状分析

### 1.1 项目概述
Canva VU 是一个轻量级的 Canva 应用，主要功能：
- 视频上传和选择
- AI 视频增强处理
- 用户认证和积分管理
- 任务状态跟踪

### 1.2 技术栈
- **前端框架**: React 18.3.1 + TypeScript 5.5.4
- **状态管理**: Zustand 5.0.2
- **异步状态**: React Query 5.80.3
- **UI组件**: @canva/app-ui-kit 4.10.0

### 1.3 核心问题
1. **Store 相互依赖**: taskStore 直接导入并修改其他 store
2. **组件职责过重**: 组件包含过多业务逻辑和状态管理
3. **代码分散**: 相关逻辑分散在多个文件中
4. **测试困难**: 紧耦合导致难以单元测试

## 2. 重构目标

1. **解耦 Store 依赖**: 消除 store 间的直接导入
2. **简化组件**: 组件只负责 UI 渲染和用户交互
3. **整合逻辑**: 将相关业务逻辑集中管理
4. **提高可测试性**: 通过简单的抽象提高测试友好性

## 3. 轻量级重构方案

### 3.1 简化架构

保持现有目录结构，只做最小必要调整：

```
src/
├── components/        # UI 组件（保持不变）
├── pages/            # 页面组件（新增，从 generate.tsx 等重构）
├── hooks/            # 自定义 hooks（保持不变）
├── services/         # 业务逻辑服务（新增）
├── stores/           # Zustand stores（重构）
├── lib/              # API 客户端（保持不变）
├── types/            # 类型定义（保持不变）
└── utils/            # 工具函数（保持不变）
```

### 3.2 核心改进点

#### 3.2.1 Store 解耦
- 使用 Zustand 的 subscribeWithSelector 实现 store 间通信
- 创建简单的事件系统替代直接导入

#### 3.2.2 提取业务逻辑
- 创建 `services` 目录存放业务逻辑
- 使用自定义 hooks 封装组件逻辑

#### 3.2.3 页面组件重构
- 将 generate.tsx, waiting.tsx 等移到 pages 目录
- 使用 hooks 分离逻辑和 UI

## 4. 具体重构方案

### 4.1 Store 解耦

#### 4.1.1 使用订阅模式替代直接导入
```typescript
// 创建简单的事件系统
// src/stores/events.ts
export const storeEvents = {
  AUTH_LOGOUT: 'auth:logout',
  TASK_CREATED: 'task:created',
  VIDEO_UPLOADED: 'video:uploaded'
};

// 使用 Zustand 的 subscribeWithSelector
// src/stores/taskStore.ts
export const useTaskStore = create<TaskState>()(
  subscribeWithSelector((set, get) => ({
    tasks: {},
    // ... 其他状态
  }))
);

// 监听认证状态变化
useAuthStore.subscribe(
  (state) => state.user,
  (user) => {
    if (!user) {
      useTaskStore.setState({ tasks: {} }); // 清空任务
    }
  }
);
```

#### 4.1.2 移除 Store 间直接导入
```typescript
// 原来：直接导入其他 store
import { useAuthStore } from './authStore';
import { useAppStore } from './appStore';

// 现在：通过订阅和事件通信
const handleTaskComplete = (taskId: string) => {
  // 只操作自己的状态
  set((state) => ({
    tasks: { ...state.tasks, [taskId]: { ...state.tasks[taskId], status: 'completed' } }
  }));
  
  // 通过事件通知其他模块
  window.dispatchEvent(new CustomEvent('task:completed', { detail: { taskId } }));
};
```

### 4.2 提取业务逻辑服务

#### 4.2.1 创建视频处理服务
```typescript
// src/services/videoService.ts
export class VideoService {
  async uploadVideo(file: File): Promise<UploadResult> {
    // 从 taskStore 中提取的上传逻辑
    // 统一处理错误和状态更新
  }
  
  async processVideo(params: ProcessingParams): Promise<Task> {
    // 从组件中提取的处理逻辑
    // 包含积分验证、任务创建等
  }
}

// 使用单例模式
export const videoService = new VideoService();
```

#### 4.2.2 创建认证服务
```typescript
// src/services/authService.ts
export class AuthService {
  async login(token: string): Promise<User> {
    // 从 authStore 中提取的登录逻辑
  }
  
  logout(): void {
    // 清理所有相关状态
    useAuthStore.getState().reset();
    useTaskStore.getState().reset();
    // 清理 localStorage
  }
}

export const authService = new AuthService();
```

### 4.3 组件重构

#### 4.3.1 使用自定义 Hook 封装逻辑
```typescript
// src/hooks/useVideoUpload.ts
export const useVideoUpload = () => {
  const { video, setVideo } = useVideoStore();
  const { setLoading } = useAppStore();
  
  const upload = useCallback(async (file: File) => {
    setLoading('upload', true);
    try {
      const result = await videoService.uploadVideo(file);
      setVideo(result.video);
      return result;
    } finally {
      setLoading('upload', false);
    }
  }, []);
  
  return { video, upload };
};
```

#### 4.3.2 简化组件
```typescript
// src/components/VideoUploader.tsx
const VideoUploader = () => {
  const { video, upload } = useVideoUpload();
  
  const handleFileSelect = async (file: File) => {
    await upload(file);
  };
  
  return (
    <div>
      <input onChange={(e) => handleFileSelect(e.target.files[0])} />
      {video && <VideoPreview video={video} />}
    </div>
  );
};
```

### 4.4 页面组件重构

#### 4.4.1 创建页面 Hook
```typescript
// src/hooks/useGeneratePage.ts
export const useGeneratePage = () => {
  const { user } = useAuthStore();
  const { video } = useVideoStore();
  const { pageMode } = useAppStore();
  
  const canGenerate = user && video;
  
  const handleGenerate = useCallback(async () => {
    if (!canGenerate) return;
    
    try {
      await videoService.processVideo({ video, settings });
      // 页面跳转逻辑
    } catch (error) {
      // 错误处理
    }
  }, [canGenerate, video]);
  
  return { user, video, canGenerate, handleGenerate };
};
```

#### 4.4.2 简化页面组件
```typescript
// src/pages/GeneratePage.tsx
const GeneratePage = () => {
  const { user, video, canGenerate, handleGenerate } = useGeneratePage();
  
  if (!user) return <SignInPrompt />;
  
  return (
    <div>
      <VideoUploader />
      <VuSettings />
      <VuGenerateButton 
        disabled={!canGenerate}
        onClick={handleGenerate}
      />
    </div>
  );
};
```

## 5. 迁移策略

### 5.1 轻量级迁移计划

#### 第一阶段：Store 解耦（1-2天）
1. 为现有 store 添加 subscribeWithSelector
2. 移除 taskStore 中的直接导入
3. 使用订阅模式替代直接调用

#### 第二阶段：提取业务逻辑（2-3天）
1. 创建 `src/services` 目录
2. 提取 taskStore 中的业务逻辑到 VideoService
3. 创建 AuthService 统一处理认证逻辑

#### 第三阶段：重构组件（2-3天）
1. 为复杂组件创建自定义 hooks
2. 简化组件职责，只保留 UI 相关逻辑
3. 将页面级组件移到 `src/pages` 目录

#### 第四阶段：测试和优化（1-2天）
1. 验证重构后功能正常
2. 优化性能问题
3. 清理无用代码

### 5.2 风险控制

1. **最小化改动**: 保持现有 API 和接口不变
2. **渐进式重构**: 每个模块可以独立重构
3. **保持测试**: 确保重构不影响现有功能
4. **回滚机制**: 每个阶段都可以快速回滚

## 6. 简化的技术实现

### 6.1 Store 订阅示例

```typescript
// src/stores/authStore.ts
export const useAuthStore = create<AuthState>()(
  subscribeWithSelector((set, get) => ({
    user: null,
    token: null,
    
    login: async (token: string) => {
      // 登录逻辑
      const user = await authService.validateToken(token);
      set({ user, token });
    },
    
    logout: () => {
      set({ user: null, token: null });
      // 通知其他 store 清理状态
    }
  }))
);

// 在其他 store 中监听
useAuthStore.subscribe(
  (state) => state.user,
  (user) => {
    if (!user) {
      useTaskStore.getState().reset();
    }
  }
);
```

### 6.2 简化的服务层

```typescript
// src/services/videoService.ts
export const videoService = {
  async uploadVideo(file: File): Promise<UploadResult> {
    // 简化的上传逻辑，从 taskStore 移动过来
    const formData = new FormData();
    formData.append('file', file);
    
    const response = await fetch('/api/upload', {
      method: 'POST',
      body: formData
    });
    
    return response.json();
  },
  
  async processVideo(params: ProcessParams): Promise<Task> {
    // 简化的处理逻辑
    const response = await fetch('/api/process', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(params)
    });
    
    return response.json();
  }
};
```

### 6.3 简化的 Hook 模式

```typescript
// src/hooks/useVideoGenerate.ts
export const useVideoGenerate = () => {
  const { user } = useAuthStore();
  const { video } = useVideoStore();
  const { setPageMode, setLoading } = useAppStore();
  
  const generate = useCallback(async (settings: GenerateSettings) => {
    if (!user || !video) return;
    
    setLoading('generate', true);
    try {
      const task = await videoService.processVideo({ video, settings });
      useTaskStore.getState().setCurrentTask(task);
      setPageMode('waiting');
    } catch (error) {
      // 错误处理
    } finally {
      setLoading('generate', false);
    }
  }, [user, video]);
  
  return { generate };
};
```

## 7. 测试策略

### 7.1 轻量级测试

```typescript
// src/services/__tests__/videoService.test.ts
describe('VideoService', () => {
  it('should upload video successfully', async () => {
    const file = new File([''], 'test.mp4');
    const result = await videoService.uploadVideo(file);
    
    expect(result).toBeDefined();
    expect(result.videoId).toBeTruthy();
  });
});

// src/hooks/__tests__/useVideoGenerate.test.ts
describe('useVideoGenerate', () => {
  it('should generate video when user and video exist', async () => {
    const { result } = renderHook(() => useVideoGenerate());
    
    await act(async () => {
      await result.current.generate(mockSettings);
    });
    
    expect(useTaskStore.getState().currentTask).toBeDefined();
  });
});
```

## 8. 总结

这个轻量级重构方案：

### 优点：
1. **最小化改动**: 保持现有结构，只做必要调整
2. **快速实施**: 整个重构可在 1-2 周内完成
3. **风险可控**: 每个阶段都可以独立验证和回滚
4. **即时收益**: 解耦后立即提升代码可维护性

### 核心改进：
1. **Store 解耦**: 使用 Zustand 订阅替代直接导入
2. **逻辑提取**: 业务逻辑从组件/store 移到 service
3. **组件简化**: 通过自定义 hooks 分离逻辑和 UI
4. **结构优化**: 轻量级的目录调整

这个方案适合轻量级应用，既解决了耦合问题，又不会引入过度复杂的架构。