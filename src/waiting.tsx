import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Text, Box } from "@canva/app-ui-kit";
import { FormattedMessage, useIntl } from "react-intl";
import { useTaskStore } from "./stores/taskStore";

export default function Waiting() {
  const progress = useTaskStore((state) => state.progress);
  const currentStep = useTaskStore((state) => state.currentStep);
  const isCancelling = useTaskStore((state) => state.isCancelling);
  const handleCancel = useTaskStore((state) => state.handleCancel);
  const intl = useIntl();

  const getStepMessage = () => {
    switch (currentStep) {
      case "uploading":
        return intl.formatMessage({
          defaultMessage: "Uploading your image...",
          description: "Status message when uploading image",
        });
      case "submitting":
        return intl.formatMessage({
          defaultMessage: "Submitting task...",
          description: "Status message when submitting task",
        });
      case "processing":
        return intl.formatMessage({
          defaultMessage: "Processing your video...",
          description: "Status message when processing video",
        });
      default:
        return intl.formatMessage({
          defaultMessage: "Preparing...",
          description: "Default status message",
        });
    }
  };

  return (
    <Box paddingEnd="2u">
      <Rows spacing="2u">
        <Title size="small" alignment="center">
          {getStepMessage()}
        </Title>
        <ProgressBar value={progress} />
        <Text alignment="center" tone="tertiary" size="small">
          <FormattedMessage
            defaultMessage="This can take a while. Feel free to close the app and return later to see the result."
            description="A notification message to inform user that we have cache to avoid waiting in the same page for the video generation."
          />
        </Text>
        <Button
          variant="secondary"
          onClick={handleCancel}
          loading={isCancelling}
          disabled={isCancelling}
        >
          {isCancelling
            ? intl.formatMessage({
                defaultMessage: "Cancelling...",
                description:
                  "Cancel button text when cancelling is in progress",
              })
            : intl.formatMessage({
                defaultMessage: "Cancel",
                description: "Cancel button text",
              })}
        </Button>
      </Rows>
    </Box>
  );
}
