import * as React from "react";
import * as styles from "styles/components.css";

import { Alert, Box } from "@canva/app-ui-kit";
import { useAuthStore } from "./stores/authStore";
import { useAppStore } from "./stores/appStore";

import Waiting from "./waiting";
import Generate from "./generate";
import Result from "./vu-result";
import { useTaskStore } from "./stores/taskStore";

const TopErrorAlert = () => {
  const { errorContent, errorTitle } = useAppStore();

  return (
    <>
      {errorContent && (
        <Alert
          tone="critical"
          title={errorTitle}
          onDismiss={() =>
            useAppStore.setState({ errorContent: "", errorTitle: "" })
          }
        >
          {errorContent}
        </Alert>
      )}
    </>
  );
};

const PageContent = () => {
  const { pageMode } = useAppStore();

  switch (pageMode) {
    case "Generate":
      return <Generate />;
    case "Waiting":
      return (
        <div className={styles.waitingContainer}>
          <Waiting />
        </div>
      );
    case "Result":
      return (
        <div className={styles.resultscrollContainer}>
          <Result />
        </div>
      );
    default:
      return null;
  }
};

export const App = () => {
  const init = async () => {
    try {
      // 首先尝试获取用户token
      const token = await useAuthStore.getState().retrieveAndSetToken();
      console.log("token", token);
      // 只有在成功获取token后才尝试恢复任务状态
      if (token) {
        await useTaskStore.getState().retrieveProcessingTaskStatus();
      } else {
        localStorage.removeItem("vu-task-storage");
      }
    } catch (error) {
      console.error("Failed to initialize app:", error);
    }
  };

  React.useEffect(() => {
    init();
  }, []);

  return (
    <div className={styles.appContainer}>
      <Box paddingTop="1u" paddingEnd="2u">
        <TopErrorAlert />
      </Box>
      <PageContent />
    </div>
  );
};

export default App;
