export async function createVideoUpScalerTask(input: {
  videoUrl: string;
  enableUpscale?: boolean;
  model?: string;
  videoQuality?: string;
  mimeType: string;
  source: "web" | "canva";
  token: string;
  baseUrl: string;
}): Promise<{ taskId: string }> {
  const response = await fetch(`${input.baseUrl}/api/video-upscaler`, {
    method: "POST",
    headers: {
      Authorization: `Bearer ${input.token}`,
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      videoUrl: input.videoUrl,
      enableUpscale: input.enableUpscale ?? true,
      model: input.model,
      videoQuality: input.videoQuality,
      mimeType: input.mimeType,
      source: input.source || "canva",
    }),
  });

  if (!response.ok) {
    const errorData: { error?: string } = await response.json();
    throw new Error(errorData.error ?? "Failed to create video upscaler task");
  }

  const data: { taskId: string } = await response.json();
  return data;
}
