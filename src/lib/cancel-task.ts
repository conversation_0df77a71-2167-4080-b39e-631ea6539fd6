export interface CancelTaskResponse {
  success: boolean;
  message?: string;
  data?: {
    task_id: string;
    status: string;
    [key: string]: any;
  };
  error?: string;
}

interface ErrorResponse {
  error: string;
}

/**
 * 取消正在进行中的任务
 * @param taskId 任务ID
 * @param token JWT token
 * @returns 取消任务的响应结果
 */
export async function cancelTask(
  baseUrl: string,
  taskId: string,
  token: string
): Promise<CancelTaskResponse> {
  try {
    const response = await fetch(`${baseUrl}/api/task/${taskId}/cancel`, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
    });

    const data = (await response.json()) as CancelTaskResponse | ErrorResponse;

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(errorText || "Failed to cancel task");
    }

    return data as CancelTaskResponse;
  } catch (error) {
    return {
      success: false,
      error: "Network error while cancelling task",
    };
  }
}
