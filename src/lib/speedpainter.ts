export async function createSpeedPainterTask(input: {
  imageUrl: string;
  sketchDuration?: number;
  colorFillDuration?: number;
  needHand?: boolean;
  mimeType?: string;
  source: "api" | "web" | "framer";

  token: string;
  baseUrl: string;
}): Promise<{ taskId: string }> {
  const response = await fetch(`${input.baseUrl}/api/speedpainter`, {
    method: "POST",
    headers: {
      Authorization: `Bearer ${input.token}`,
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      imageUrl: input.imageUrl,
      sketchDuration: input.sketchDuration ?? 3,
      colorFillDuration: input.colorFillDuration ?? 3,
      needHand: input.needHand ?? false,
      mimeType: input.mimeType ?? "image/jpg",
      source: input.source,
    }),
  });

  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(errorText || "Failed to create speed painter task");
  }

  return response.json();
}
