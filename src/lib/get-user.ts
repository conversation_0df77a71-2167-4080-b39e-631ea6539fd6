export interface UserInfo {
  name: string | null
  email: string | null
  picture_url: string | null
  primary_owner_user_id: string | null
}

export interface GetUserResponse {
  success: boolean
  data?: UserInfo
  error?: string
}

export async function getUserInfo(input: {
  token: string
  baseUrl: string
}): Promise<GetUserResponse> {
  const response = await fetch(`${input.baseUrl}/api/get-user-info`, {
    method: 'GET',
    headers: {
      Authorization: `Bearer ${input.token}`,
      'Content-Type': 'application/json',
    },
  })

  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(errorText || "get user info failed");
  }

  return response.json()
} 