import type { AppType } from './types'

export type FileUploadBase64Response = {
  url: string
}

export async function uploadBase64File(input: {
  base64Data: string
  fileName: string
  app: AppType
  mimeType?: string
  token: string
  baseUrl: string
}): Promise<FileUploadBase64Response> {
  const response = await fetch(`${input.baseUrl}/api/file-upload-base64`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${input.token}`,
      'Content-Type': 'application/json'
    },
    signal: AbortSignal.timeout(30000), // 30 second timeout
    body: JSON.stringify({
      base64Data: input.base64Data,
      fileName: input.fileName,
      app: input.app,
      mimeType: input.mimeType ?? 'image/jpeg'
    })
  })

  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(errorText || "Failed to upload base64 file");
  }

  return response.json()
} 