// Credit types
export interface CreditRecord {
  id?: number
  account_id: string
  pending_credits: number
  plan_credits: number
  pay_as_go_credits: number
  created_at: Date
  updated_at: Date
}

export interface CreditResponse {
  success: boolean
  data: CreditRecord
  code?: string
  message?: string
}

export type StripeAddPayAsGoInput = {
  uid: string
  credits: number
  stripeSignature: string
  accountId: string
}

export interface StripeResetPlanCreditInput {
  uid: string
  credits: number
  stripeSignature: string
  accountId: string
}

export class CreditClient {
  private readonly baseUrl: string
  private readonly token: string

  constructor(opts: { token: string; baseUrl: string }) {
    this.token = opts.token
    this.baseUrl = opts.baseUrl
  }

  async getUserCredit(): Promise<CreditResponse> {
    const response = await fetch(`${this.baseUrl}/api/get-credit`, {
      method: 'GET',
      headers: {
        Authorization: `Bearer ${this.token}`,
        'Content-Type': 'application/json',
      },
    })

    if (!response.ok) {
      throw new Error((await response.text()) ?? 'Failed to get user credit')
    }

    return response.json() as Promise<CreditResponse>
  }

  async stripeAddPayAsGoCredit(
    input: StripeAddPayAsGoInput,
  ): Promise<CreditResponse> {
    const response = await fetch(
      `${this.baseUrl}/api/stripe/add-pay-as-you-go`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(input),
      },
    )

    if (!response.ok) {
      const errorData: { error?: string } = await response.json()
      throw new Error(
        errorData.error ?? 'Failed to add pay as go credit via Stripe',
      )
    }

    return response.json() as Promise<CreditResponse>
  }

  async stripeResetPlanCredit(
    input: StripeResetPlanCreditInput,
  ): Promise<CreditResponse> {
    const response = await fetch(
      `${this.baseUrl}/api/stripe/reset-plan-credits`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(input),
      },
    )

    if (!response.ok) {
      const errorData: { error?: string } = await response.json()
      throw new Error(
        errorData.error ?? 'Failed to reset plan credit via Stripe',
      )
    }

    return response.json() as Promise<CreditResponse>
  }
}
