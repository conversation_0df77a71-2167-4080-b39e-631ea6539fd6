// Keep exchangeToken as a standalone function
export async function exchangeToken(input: {
  source: 'web';
  token: string;
  baseUrl: string;
}): Promise<string> {
    const response = await fetch(`${input.baseUrl}/api/exchange-token`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(input),
    });

  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(errorText || "Token exchange failed");
  }

    const data = (await response.json()) as { token: string };
    return data.token;
}
