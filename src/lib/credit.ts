import { baseUrl } from "./index";

export interface CalculateCreditsInput {
  data: Array<{
    app: string;
    source: string;
    [key: string]: any;
  }>;
}

export interface CalculateCreditsResponse {
  needCredits: number;
}

export async function calculateCredits(
  input: CalculateCreditsInput
): Promise<CalculateCreditsResponse> {
  const response = await fetch(`${baseUrl}/api/calculate-credits`, {
    method: "POST",
    headers: {
      // Authorization: `Bearer ${token}`,
      "Content-Type": "application/json",
    },
    body: JSON.stringify(input),
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(
      JSON.stringify({
        message: "Failed to calculate credits",
        code: "CALCULATE_CREDITS_ERROR",
        status: response.status,
        statusText: response.statusText,
        details: errorData,
      })
    );
  }

  return response.json() as Promise<CalculateCreditsResponse>;
}

