import { events } from "fetch-event-stream";
import { AppType, SourceType } from "./types";

export type TaskStatus = {
  taskId: string;
} & (
  | {
      status: "WAITING";
    }
  | {
      status: "PROCESSING";
    }
  | {
      status: "INIT";
    }
  | {
      status: "UNKNOWN";
    }
  | {
      status: "FAILED";
    }
  | {
      status: "FINISHED";
      videoUrl: string;
      thumbUrl: string;
    }
  | {
      status: "ERROR";
      error: string;
    }
);

export async function* getTaskStatusStream(input: {
  taskId: string;
  app: AppType;
  source?: SourceType;
  signal?: AbortSignal;
  token: string;
  baseUrl: string;
}): AsyncGenerator<TaskStatus> {
  const url = new URL(`${input.baseUrl}/api/task/${input.taskId}/sse`);
  url.searchParams.set("app", input.app);
  if (input.source) {
    url.searchParams.set("source", input.source);
  }

  const response = await fetch(url.toString(), {
    signal: input.signal,
    headers: {
      Authorization: `Bearer ${input.token}`,
    },
  });

  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(errorText || "Failed to get task status");
  }

  for await (const event of events(response)) {
    if (event.data) {
      const payload = JSON.parse(event.data) as TaskStatus;
      yield payload;
    }
  }
}
