import React from "react";
import { useQuery } from "@tanstack/react-query";

import { calculateCredits } from "../lib/credit";
import { useAuthStore } from "../stores/authStore";

export function useVideoUpscalerCredits(
  videoDuration: number,
  enableUpscale: boolean,
  model?: string,
  videoQuality?: string
) {
  const { credit } = useAuthStore();
  const fallbackCredits =
    enableUpscale && videoDuration > 0 ? Math.ceil(videoDuration) * 1 : 0;

  const {
    data: needCredits = fallbackCredits,
    isLoading,
    error,
  } = useQuery({
    queryKey: [
      "video-upscaler-credits",
      videoDuration,
      enableUpscale,
      model,
      videoQuality,
    ],
    queryFn: async () => {
      const result = await calculateCredits({
        data: [
          {
            app: "vu",
            source: "web",
            videoDuration,
            enableUpscale,
            model,
            videoQuality,
          },
        ],
      });
      return result.needCredits;
    },
    enabled: enableUpscale && videoDuration > 0,
    staleTime: 5 * 60 * 1000,
    retry: 2,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 5000),
    placeholderData: fallbackCredits,
  });

  // 记录错误日志
  if (error) {
    console.error("Failed to calculate video upscaler credits:", error);
  }

  // 计算可用积分
  const availableCredits = React.useMemo(() => {
    return (
      (credit?.plan_credits || 0) +
      (credit?.pay_as_go_credits || 0) -
      (credit?.pending_credits || 0)
    );
  }, [
    credit?.plan_credits,
    credit?.pay_as_go_credits,
    credit?.pending_credits,
  ]);

  const canExecute =
    availableCredits >= needCredits && !isLoading && error === null;

  return {
    needCredits,
    canExecute,
    isLoading,
    error: error ? String(error) : null,
  };
}
