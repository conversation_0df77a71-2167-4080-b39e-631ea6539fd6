import React, { useRef, useState } from "react";
import { useVideoStore } from "../stores/videoStore";

import { selection } from "@canva/design";
import { getTemporaryUrl } from "../../sdk/preview/asset";

import { useIntl } from "react-intl";
import { useAppStore } from "../stores/appStore";
import { SUPPORTED_VIDEO_TYPES } from "../common/constants";
import { getVideoResolution } from "src/common/util";

export function useVideoSelection() {
  const usedContentRef = useRef<string | null>(null);
  const intl = useIntl();
  const [registerOnChangeEventRead, setRegisterOnChangeEventRead] =
    React.useState<any>(null);
  const {
    setVideoLoading,
    setUploadTip,
    setVideoSrc,
    setVideoMimeType,
    setVideoInfo,
    setThumbnailUrl,
    setFileName,
    setVideoSize,
    setVideoUrl,
  } = useVideoStore();
  const pageMode = useAppStore((state) => state.pageMode);

  // 重置状态函数
  const resetState = React.useCallback(() => {
    setVideoInfo({
      src: null,
      name: "",
      type: "",
      size: 0,
      duration: 0,
      thumbnailUrl: "",
      url: null,
    });
    setVideoSize({
      width: 0,
      height: 0,
    });
  }, [setVideoInfo, setVideoSize]);

  const getVideoType = (videoUrl: string): string => {
    const url = new URL(videoUrl);
    const pathSegments = url.pathname.split("/");
    const fileName = pathSegments[pathSegments.length - 1];
    const format = fileName.split(".").pop() as string;
    return `video/${format}`;
  };

  const handleVideoUrl = React.useCallback(
    async (url: string, size: number, ref: string) => {
      // 从视频链接，获取视频的格式
      const mimeType = getVideoType(url);

      if (SUPPORTED_VIDEO_TYPES.includes(mimeType)) {
        console.log("mimeType is supported");
        if (usedContentRef.current !== ref) {
          setVideoLoading(false);
          return;
        }
        var video = document.createElement("video");
        video.preload = "metadata";
        video.crossOrigin = "anonymous"; // 或者 'use-credentials'
        video.src = url;
        video.onloadedmetadata = function () {
          const videoSize = {
            width: video.videoWidth,
            height: video.videoHeight,
          };
          video.currentTime = 0.1;
          video.onseeked = () => {
            const canvas = document.createElement("canvas");
            canvas.width = video.videoWidth;
            canvas.height = video.videoHeight;
            const context = canvas.getContext("2d") as CanvasRenderingContext2D;
            context.drawImage(video, 0, 0, canvas.width, canvas.height);
            const thumbnailUrl = canvas.toDataURL("image/png");
            setThumbnailUrl(thumbnailUrl);
            const resolution = getVideoResolution(video);
            setVideoInfo({
              src: null,
              url: url,
              name: "video",
              type: mimeType,
              size: size,
              duration: video.duration,
              thumbnailUrl: thumbnailUrl,
              resolution: resolution,
            });
            setVideoSize(videoSize);
            setFileName(`video`);
            setVideoMimeType(mimeType);
            setVideoLoading(false);
          };
        };
      } else {
        setUploadTip({
          title: intl.formatMessage({
            defaultMessage: "Unsupported video format.",
            description: "An error message when the input video is corrupted.",
          }),
          desc: intl.formatMessage({
            defaultMessage: "Please try something else.",
            description: "An error message when the input video is corrupted.",
          }),
          tone: "critical",
        });
      }
    },
    [
      intl,
      setVideoLoading,
      setUploadTip,
      setThumbnailUrl,
      setVideoInfo,
      setVideoSize,
      setFileName,
      setVideoMimeType,
    ]
  );

  // 注册选择变化事件
  React.useEffect(() => {
    const disposer = selection.registerOnChange({
      scope: "video",
      onChange: async (event: any) => {
        setUploadTip({
          title: "",
          desc: "",
          tone: "critical",
        });
        if (event.count > 1) {
          setVideoLoading(false);
          setUploadTip({
            title: intl.formatMessage({
              defaultMessage: "Multiple elements selected.",
              description:
                "An error message when multiple elements are selected.",
            }),
            desc: intl.formatMessage({
              defaultMessage: "Please select only one element at a time.",
              description:
                "An error message when multiple elements are selected.",
            }),
            tone: "critical",
          });
          usedContentRef.current = null;
          setVideoLoading(false);
          return;
        } else if (event.count === 1) {
          try {
            const readValue = await event.read();
            const refs = readValue.contents.map((c) => c.ref);
            if (pageMode === "Generate") {
              setRegisterOnChangeEventRead(readValue);
              const contents = readValue.contents;
              const contentRef = contents[0].ref;
              if (usedContentRef.current !== contentRef) {
                setVideoLoading(true);
                resetState();
                usedContentRef.current = contentRef;
                let result = await getTemporaryUrl({
                  type: "VIDEO",
                  ref: contentRef,
                });
                setVideoSrc(result.url);
                setVideoUrl(result.url);
                if (usedContentRef.current !== contentRef) {
                  return;
                }
                try {
                  const fetchData = await fetch(result.url);
                  const blob = await fetchData.blob();
                  handleVideoUrl(result.url, blob.size, contentRef);
                } catch (error) {
                  console.error("Selection error:", error);
                  setVideoLoading(false);
                  setUploadTip({
                    title: intl.formatMessage({
                      defaultMessage: "Unable to load selected video.",
                      description:
                        "An error message when the input video is corrupted.",
                    }),
                    desc: intl.formatMessage({
                      defaultMessage:
                        "Please wait until the video is fully loaded in canva editor.",
                      description:
                        "An error message when the input video is corrupted.",
                    }),
                    tone: "critical",
                  });
                }
              }
            }
          } catch (error) {
            console.error("Selection error:", error);
            setVideoLoading(false);
            setUploadTip({
              title: intl.formatMessage({
                defaultMessage: "Unable to load video.",
                description:
                  "An error message when the input video is corrupted.",
              }),
              desc: intl.formatMessage({
                defaultMessage: "Please try again or select something else.",
                description:
                  "An error message when the input video is corrupted.",
              }),
              tone: "critical",
            });
          }
        } else if (usedContentRef.current) {
          usedContentRef.current = null;
          setVideoLoading(false);
          resetState();
        }
      },
    });

    // 返回 cleanup 函数
    return disposer;
  }, [
    intl,
    pageMode,
    setVideoLoading,
    setUploadTip,
    setVideoSrc,
    setVideoUrl,
    resetState,
    setRegisterOnChangeEventRead,
    handleVideoUrl,
  ]);

  return {};
}
