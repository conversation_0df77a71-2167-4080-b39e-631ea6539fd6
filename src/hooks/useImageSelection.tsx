import { useIntl } from "react-intl";
import { imageToBase64 } from "utils/tools";
import * as React from "react";
import { getTemporaryUrl } from "../../sdk/preview/asset";
import { selection } from "../../sdk/preview/design";
import { SelectionEvent } from "../../sdk/preview/design";
import { ImageInfo, UploadTip } from "../types/generate";
import { useImageStore } from "src/stores/imageStore";
import { useAppStore } from "src/stores/appStore";

// Constants
const SUPPORTED_IMAGE_TYPES = [
  "image/png",
  "image/jpeg",
  "image/jpg",
  "image/heic",
  "image/webp",
] as const;
const MAX_FILE_SIZE = 6 * 1024 * 1024; // 6MB
const COMPRESSION_THRESHOLD = 2 * 1024 * 1024; // 2MB

let usedContentRef: any = null;

export function useImageSelection() {
  const intl = useIntl();
  const [registerOnChangeEventRead, setRegisterOnChangeEventRead] =
    React.useState<any>(null);

  const {
    setImgLoading,
    setUploadTip,
    setImgUrl,
    setImgMimeType,
    setImageInfo,
  } = useImageStore();
  const pageMode = useAppStore((state) => state.pageMode);

  // 重置状态函数
  const resetState = React.useCallback(() => {
    setImageInfo({
      imgSrc: null,
      imgSize: { width: 0, height: 0 },
      fromType: "",
      imgMimeType: "",
    });
  }, [setImageInfo]);

  const showErrorTip = () => {
    setUploadTip({
      title: intl.formatMessage({
        defaultMessage: "Image corrupted.",
        description: "Error message when image is corrupted",
      }),
      desc: intl.formatMessage({
        defaultMessage: "Try a different one.",
        description: "Suggestion when image is corrupted",
      }),
      tone: "critical",
    });
  };

  // 处理图片文件
  const handleFile = React.useCallback(
    async (imgFile: File, contentRef: string) => {
      try {
        const size = imgFile.size;
        const type = imgFile.type;

        setUploadTip({ title: "", desc: "", tone: "critical" });

        if (!SUPPORTED_IMAGE_TYPES.includes(type as any)) {
          setUploadTip({
            title: intl.formatMessage({
              defaultMessage:
                "Speed Painter currently doesn't support this element.",
              description:
                "Speed Painter currently doesn't support this element.",
            }),
            desc: intl.formatMessage({
              defaultMessage: "Please try an image.",
              description: "Please try an image.",
            }),
            tone: "critical",
          });
          return;
        }

        if (usedContentRef !== contentRef) return;

        if (size > MAX_FILE_SIZE) {
          setUploadTip({
            title: intl.formatMessage({
              defaultMessage: "That file is too large.",
              description: "Error message when file size exceeds limit",
            }),
            desc: intl.formatMessage({
              defaultMessage: "Please choose one that's smaller than 6MB.",
              description: "Description for file size limit error",
            }),
            tone: "critical",
          });
          return;
        }

        const base64 = await new Promise<string>((resolve) => {
          const reader = new FileReader();
          reader.onload = (e) => resolve(e.target?.result as string);
          reader.readAsDataURL(imgFile);
        });

        const img = await new Promise<HTMLImageElement>((resolve) => {
          const img = new Image();
          img.onload = () => resolve(img);
          img.src = base64;
        });

        const imgSize = {
          width: img.width,
          height: img.height,
        };

        if (size <= COMPRESSION_THRESHOLD) {
          setImageInfo({
            imgSrc: base64,
            imgSize,
            fromType: "selection",
            imgMimeType: type,
          });
        } else {
          const obj = imageToBase64(img, type);
          if (obj.imgBase64) {
            setImageInfo({
              imgSrc: obj.imgBase64,
              imgSize: {
                width: obj.width,
                height: obj.height,
              },
              fromType: "selection-base64",
              imgMimeType: type,
            });
          } else {
            showErrorTip();
          }
        }
      } catch (error) {
        console.error("File handling error:", error);
        showErrorTip();
      }
    },
    [usedContentRef, setImageInfo, setImgMimeType]
  );

  // 处理多选情况
  const handleMultipleSelection = React.useCallback(() => {
    setImgLoading(false);
    setUploadTip({
      title: intl.formatMessage({
        defaultMessage: "Multiple elements selected.",
        description: "Error message when multiple elements are selected",
      }),
      desc: intl.formatMessage({
        defaultMessage: "Please select only one element at a time.",
        description: "Suggestion when multiple elements are selected",
      }),
      tone: "critical",
    });
    usedContentRef = null;
    resetState();
  }, [setImgLoading, setUploadTip, resetState]);

  // 处理单个选择
  const handleSingleSelection = React.useCallback(
    async (readValue: any) => {
      if (pageMode === "Generate") {
        setRegisterOnChangeEventRead(readValue);
        const contentRef = readValue.contents[0].ref;
        if (usedContentRef !== contentRef) {
          setImgLoading(true);
          resetState();
          usedContentRef = contentRef;

          try {
            const result = await getTemporaryUrl({
              type: "IMAGE",
              ref: contentRef,
            });
            const fetchData = await fetch(result.url);
            setImgUrl(result.url);
            if (usedContentRef !== contentRef) return;
            const blob = await fetchData.blob();
            if (usedContentRef !== contentRef) return;
            const mimeType = blob.type;
            if (!mimeType.startsWith("image")) {
              showErrorTip();
              return;
            }
            setImgMimeType(mimeType);
            const imgFile = new File([blob], "name", { type: mimeType });
            await handleFile(imgFile, contentRef);
            setImgLoading(false);
          } catch (error) {
            setImgLoading(false);
            showErrorTip();
            console.error("Selection error:", error);
          }
        }
      }
    },
    [
      pageMode,
      usedContentRef,
      setImgLoading,
      resetState,
      setImgUrl,
      setImgMimeType,
      handleFile,
    ]
  );

  // 注册选择变化事件
  React.useEffect(() => {
    if (!registerOnChangeEventRead) {
      selection.registerOnChange({
        scope: "image",
        onChange: async (event: SelectionEvent<"image">) => {
          if (event.count > 0) {
            if (event.count > 1) {
              handleMultipleSelection();
            } else {
              try {
                const readValue = await event.read();
                await handleSingleSelection(readValue);
              } catch (error) {
                setImgLoading(false);
                showErrorTip();
                console.error("Selection error:", error);
              }
            }
          } else if (usedContentRef) {
            usedContentRef = null;
            setImgLoading(false);
            resetState();
          }
        },
      });
    }
  }, [
    registerOnChangeEventRead,
    handleMultipleSelection,
    handleSingleSelection,
    usedContentRef,
    setImgLoading,
    resetState,
    showErrorTip,
  ]);

  return {
    handleDelete: resetState,
  };
}
