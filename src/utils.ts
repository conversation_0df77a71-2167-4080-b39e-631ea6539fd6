import sha256 from "sha256";

const createXSign = () => {
  // 直接获取当前UTC时间的毫秒数
  const utcMilliSeconds = new Date().getTime();
  // 使用UTC时间戳生成签名
  const sign = sha256(Math.floor(utcMilliSeconds * 1.01).toString())
    .toUpperCase()
    .substring(0, 32);
  // 返回UTC时间戳和签名的组合
  return utcMilliSeconds.toString() + sign;
};

// add taskId
export const track = (type: string, taskId: string, extraInfo: string = "") => {
  const xSign = createXSign();
  fetch(
    `https://dj7jvjeb1v6pc.cloudfront.net/prod/user-behavior-record/record`,
    {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      mode: "cors",
      body: JSON.stringify({
        operation: "user-behavior-record",
        sign: xSign,
        payload: {
          id: taskId,
          scenes: "speed-painter",
          eventType: type,
          extraInfo: extraInfo,
        },
      }),
    }
  );
};
