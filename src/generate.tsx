// src/generate.tsx
import { Rows, Box } from "@canva/app-ui-kit";

import { GenerateButton } from "./components/VuGenerateButton";
import { useVideoSelection } from "./hooks/useVideoSelection";
import { UserInfo } from "./components/UserInfo";
import * as styles from "styles/components.css";
import { useAppStore } from "./stores/appStore";
import { SignInToGenerateButton } from "./components/SignInToGenerateButton";
import { useAuthStore } from "./stores/authStore";
import { useVideoStore } from "./stores/videoStore";
import { VideoUploader } from "./components/VideoUploader";
import { VuSettings } from "./components/VuSettings";

export default function Generate() {
  const { videoSrc, duration } = useVideoStore();
  const pageMode = useAppStore((state) => state.pageMode);
  const login = useAuthStore((state) => state.login);
  const a1dToken = useAuthStore((state) => state.a1dToken);
  const loading = useAuthStore((state) => state.loading);

  const isAuthorized = !!a1dToken;
  const showSignInButton = !videoSrc && !isAuthorized && !loading;

  // 触发canva 单选或者多选选中element
  useVideoSelection();

  if (pageMode !== "Generate") {
    return null;
  }

  return (
    <div className={styles.scrollContainer} id="generateContainer">
      <Rows spacing="1u">
        <VideoUploader />
        {videoSrc && (
          <>
            <VuSettings />
            <GenerateButton />
            {isAuthorized && (
              <Box paddingTop="1.5u">
                <UserInfo showCta={true} />
              </Box>
            )}
          </>
        )}
        {showSignInButton && <SignInToGenerateButton onClick={login} />}
        {!videoSrc && isAuthorized && <UserInfo showCta={false} />}
      </Rows>
    </div>
  );
}
