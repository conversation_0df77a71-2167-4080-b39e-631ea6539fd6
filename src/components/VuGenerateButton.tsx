// src/components/GenerateButton.tsx
import { Box, Button, Text, Alert, Link } from "@canva/app-ui-kit";

import { useIntl, FormattedMessage } from "react-intl";

import { useVideoStore } from "../stores/videoStore";
import { useAuthStore } from "../stores/authStore";
import { useAppStore } from "src/stores/appStore";
import { usePlatformInfo } from "../hooks/usePlatformInfo";
import { siteUrl } from "src/lib";
import { requestOpenExternalUrl } from "@canva/platform";
import { useTaskStore } from "src/stores/taskStore";
import { useVuGenerateSettingStore } from "src/stores/vuGenerateStore";
import { VuGenerateData } from "src/types/generate";
import React, { useEffect } from "react";
import { SignInToGenerateButton } from "./SignInToGenerateButton";
import { useVideoUpscalerCredits } from "../hooks/useVideoCredits";
import { TouchOrMouseEvent } from "@canva/app-ui-kit/dist/cjs/ui/apps/developing/ui_kit/components/button/button";

export function GenerateButton() {
  const intl = useIntl();
  const { videoSrc, videoUrl, duration, videoMimeType, setUploadTip } =
    useVideoStore();

  const { a1dToken, login } = useAuthStore();
  const buttonLoading = useAppStore((state) => state.buttonLoading);
  const { showCreditNotEnough } = useAppStore();
  const handleVuGenerate = useTaskStore((state) => state.handleVuGenerate);
  const platformInfo = usePlatformInfo();

  const isAuthorized = !!a1dToken;
  const { videoQuality, enableUpscale, model, setDuration } =
    useVuGenerateSettingStore();

  useEffect(() => {
    setDuration(duration);
  }, [duration]);

  // 使用新的 useVideoCredits hook
  const {
    canExecute,
    needCredits,
    isLoading: creditsLoading,
  } = useVideoUpscalerCredits(duration, enableUpscale, model, videoQuality);

  const handleBuyMoreClick = () => {
    requestOpenExternalUrl({
      url: `${siteUrl}/pricing`,
    });
  };

  const generateButton = () => {
    return (
      <Button
        variant="primary"
        stretch
        loading={buttonLoading}
        onClick={handleButtonClick}
        iconPosition="end"
      >
        {intl.formatMessage({
          defaultMessage: "Generate",
          description: "Generate button",
        })}
      </Button>
    );
  };

  const handleButtonClick = async (event: TouchOrMouseEvent<any>) => {
    handleCallback(event);
  };

  const handleCallback = (event: TouchOrMouseEvent<any>) => {
    event.preventDefault();
    event.stopPropagation();

    if (!canExecute) {
      useAppStore.setState({
        showCreditNotEnough: true,
      });
      return;
    }

    if (!videoUrl && !videoSrc) {
      setUploadTip({
        title: intl.formatMessage({
          defaultMessage: "Please select an video.",
          description: "An error message when no video is selected as input.",
        }),
        desc: "",
        tone: "critical",
      });
      return;
    }

    const generateData = {
      videoQuality,
      enableUpscale,
      videoSrc: videoSrc || "",
      videoUrl: videoUrl || "",
      videoMimeType,
      model,
    } satisfies VuGenerateData;

    handleVuGenerate(generateData);
  };

  return (
    <Box flexDirection="column" paddingTop="2u">
      {showCreditNotEnough && (
        <Box paddingBottom="1u">
          <Alert
            tone="critical"
            title={intl.formatMessage(
              {
                defaultMessage:
                  "Upscaling a video requires at least {needCredits} Video Upscaler credits.",
                description: "Title for insufficient credits error",
              },
              {
                needCredits,
              }
            )}
            onDismiss={() =>
              useAppStore.setState({ showCreditNotEnough: false })
            }
          >
            {platformInfo.canAcceptPayments ? (
              <FormattedMessage
                defaultMessage="Visit {buyMoreLink} to buy more Video Upscaler credits."
                description="Error message when payments are enabled and user has insufficient credits"
                values={{
                  needCredits: <strong>{needCredits}</strong>,
                  buyMoreLink: (
                    <Link
                      href={`${siteUrl}/pricing`}
                      requestOpenExternalUrl={handleBuyMoreClick}
                    >
                      <FormattedMessage
                        defaultMessage="our pricing page"
                        description="Link text to pricing page"
                      />
                    </Link>
                  ),
                }}
              />
            ) : (
              <FormattedMessage
                defaultMessage="Open this app in your web browser to purchase more Video Upscaler credits."
                description="Error message when payments are disabled and user has insufficient credits"
                values={{
                  needCredits: <strong>{needCredits}</strong>,
                }}
              />
            )}
          </Alert>
        </Box>
      )}
      {isAuthorized ? (
        generateButton()
      ) : (
        <SignInToGenerateButton onClick={login} />
      )}
    </Box>
  );
}
