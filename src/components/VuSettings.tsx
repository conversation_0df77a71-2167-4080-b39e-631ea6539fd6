// src/components/DurationSettings.tsx
import {
  Box,
  RadioGroup,
  Switch,
  Text,
  SegmentedControl,
} from "@canva/app-ui-kit";
import * as React from "react";
import { FormattedMessage, useIntl } from "react-intl";
import { useVuGenerateSettingStore } from "src/stores/vuGenerateStore";

export function VuSettings() {
  const intl = useIntl();
  const {
    videoQuality,
    enableUpscale,
    model,
    setEnableUpscale,
    setVideoQuality,
    setModel,
  } = useVuGenerateSettingStore();

  const qualityOptions = [
    { label: "480p", value: "480P" },
    { label: "720p", value: "720P" },
    { label: "1080p ᴴᴰ", value: "1080P" },
    { label: "4K ᵁᴴᴰ", value: "4K" },
  ];

  return (
    <>
      <Box paddingTop="1u">
        {/* <Text size="medium" variant="bold" tone="primary"> */}
        <Switch
          label="Upscale resolution"
          value={enableUpscale}
          onChange={(value) => setEnableUpscale(value)}
        />
        {/* </Text> */}
      </Box>

      {enableUpscale && (
        <>
          <Box paddingTop="1u">
            <Text size="medium" variant="bold" tone="primary">
              <FormattedMessage
                defaultMessage="Select quality"
                description="Choose the desired resolution for the upscale process."
              />
            </Text>
          </Box>
          <Box>
            <SegmentedControl
              value={videoQuality}
              options={qualityOptions}
              onChange={(value) => setVideoQuality(value)}
            />
          </Box>
        </>
      )}
      <Box paddingTop="1u">
        <Text size="medium" variant="bold" tone="primary">
          <FormattedMessage
            defaultMessage="Enhancement style"
            description="Choose the enhancement style for different type of video"
          />
        </Text>
      </Box>
      <Box>
        <RadioGroup
          defaultValue={model}
          options={
            [
              {
                label: intl.formatMessage({
                  defaultMessage: "General",
                  description: "General type video",
                }),
                value: "general",
                description: intl.formatMessage({
                  defaultMessage: "Improves overall clarity in all videos",
                  description: "Description for general type video",
                }),
              },
              {
                label: intl.formatMessage({
                  defaultMessage: "Anime",
                  description: "Anime type video",
                }),
                value: "anime",
                description: intl.formatMessage({
                  defaultMessage:
                    "Preserves intricate detail and vibrant colors",
                  description: "Description for anime type video",
                }),
              },
              {
                label: intl.formatMessage({
                  defaultMessage: "Portrait",
                  description: "Portrait type video",
                }),
                value: "portrait",
                description: intl.formatMessage({
                  defaultMessage: "Highlights skin tones and facial features",
                  description: "Description for portrait type video",
                }),
              },
            ] as any[]
          }
          onChange={(value) => setModel(value)}
        />
      </Box>
    </>
  );
}
