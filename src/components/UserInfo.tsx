import React from "react";
import { <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Link } from "@canva/app-ui-kit";
import { FormattedMessage, useIntl } from "react-intl";
import { useAuthStore } from "../stores/authStore";

import { siteUrl } from "src/lib";
import { requestOpenExternalUrl } from "@canva/platform";
import { usePlatformInfo } from "../hooks/usePlatformInfo";
import { useVuGenerateSettingStore } from "src/stores/vuGenerateStore";
import { useVideoStore } from "../stores/videoStore";
import { useVideoUpscalerCredits } from "../hooks/useVideoCredits";

// Types
interface CreditInfoProps {
  availableCredits: number;
  creditCost: number;
  canAcceptPayments: boolean;
  showCta: boolean;
}

interface UserProfileProps {
  name: string;
  email: string;
}

// Helper Components
const CreditInfo: React.FC<CreditInfoProps> = ({
  availableCredits,
  canAcceptPayments,
  creditCost,
  showCta,
}) => {
  const handleBuyMoreClick = () => {
    requestOpenExternalUrl({
      url: `${siteUrl}/pricing`,
    });
  };

  if (canAcceptPayments && showCta) {
    return (
      <Text alignment="center" tone="secondary">
        <FormattedMessage
          defaultMessage="Use {creditCost} of {availableCredits} Video Upscaler credits."
          description="Information about credit usage for video upscaling"
          values={{
            creditCost: <strong>{creditCost}</strong>,
            availableCredits: <strong>{availableCredits}</strong>,
          }}
        />
        <br />
        <FormattedMessage
          defaultMessage="Want more Video Upscaler credits? {buyMoreLink}"
          description="Prompt to buy more credits with link"
          values={{
            buyMoreLink: (
              <Link
                href={`${siteUrl}/pricing`}
                requestOpenExternalUrl={handleBuyMoreClick}
              >
                <FormattedMessage
                  defaultMessage="Buy more"
                  description="Link text to buy more credits"
                />
              </Link>
            ),
          }}
        />
      </Text>
    );
  }

  if (!canAcceptPayments && showCta) {
    return (
      <Text alignment="center" tone="secondary">
        <FormattedMessage
          defaultMessage="Use {creditCost} of {availableCredits} Video Upscaler credits. Open this app in your web browser to purchase more."
          description="Information about credit usage when payment links are disabled"
          values={{
            creditCost: <strong>{creditCost}</strong>,
            availableCredits: <strong>{availableCredits}</strong>,
          }}
        />
      </Text>
    );
  }

  return (
    <Text alignment="center" tone="secondary">
      {availableCredits === 0 ? (
        canAcceptPayments ? (
          <FormattedMessage
            defaultMessage="You have {creditCount} Video Upscaler credits. To continue upscaling videos, {buyMoreLink}."
            description="Message when user has no credits available and payments are enabled"
            values={{
              creditCount: <strong>0</strong>,
              buyMoreLink: (
                <Link
                  href={`${siteUrl}/pricing`}
                  requestOpenExternalUrl={handleBuyMoreClick}
                >
                  <FormattedMessage
                    defaultMessage="buy more credits"
                    description="Link text to buy more credits when user has no credits"
                  />
                </Link>
              ),
            }}
          />
        ) : (
          <FormattedMessage
            defaultMessage="You have {creditCount} Video Upscaler credits. To continue upscaling videos, open this app in your web browser to purchase more."
            description="Message when user has no credits available and payments are disabled"
            values={{
              creditCount: <strong>0</strong>,
            }}
          />
        )
      ) : (
        <FormattedMessage
          defaultMessage="You have {creditCount} Video Upscaler credits. Boost your video quality in just a few clicks!"
          description="Message when user has credits available"
          values={{
            creditCount: <strong>{availableCredits}</strong>,
          }}
        />
      )}
    </Text>
  );
};

const UserProfile: React.FC<UserProfileProps> = ({ name, email }) => (
  <Text alignment="center" size="xsmall" tone="tertiary">
    <FormattedMessage
      defaultMessage="You are logged in as {name} ({email})"
      description="User login status information"
      values={{
        name,
        email,
      }}
    />
  </Text>
);

interface UserInfoProps {
  showCta: boolean;
}

// Main Component
export const UserInfo: React.FC<UserInfoProps> = ({ showCta }) => {
  const intl = useIntl();
  const { userInfo, credit, logout } = useAuthStore();

  const platformInfo = usePlatformInfo();
  const { duration } = useVideoStore();
  const { videoQuality, enableUpscale, model } = useVuGenerateSettingStore();

  // 使用新的 useVideoCredits hook
  const { needCredits } = useVideoUpscalerCredits(
    duration,
    enableUpscale,
    model,
    videoQuality
  );

  const availableCredits = React.useMemo(() => {
    return (
      (credit?.plan_credits || 0) +
      (credit?.pay_as_go_credits || 0) -
      (credit?.pending_credits || 0)
    );
  }, [
    credit?.plan_credits,
    credit?.pay_as_go_credits,
    credit?.pending_credits,
  ]);

  if (!userInfo || !userInfo.name || !userInfo.email) {
    return null;
  }

  return (
    <Box paddingY="1u">
      <Rows spacing="1u" align="center">
        <CreditInfo
          creditCost={needCredits}
          availableCredits={availableCredits}
          canAcceptPayments={platformInfo.canAcceptPayments}
          showCta={showCta}
        />
        <UserProfile name={userInfo.name} email={userInfo.email} />

        <Button variant="secondary" onClick={logout} stretch>
          {intl.formatMessage({
            defaultMessage: "Logout",
            description: "Logout button text",
          })}
        </Button>
      </Rows>
    </Box>
  );
};

export default UserInfo;
