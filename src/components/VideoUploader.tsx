import { useEffect, useState } from "react";
import {
  Alert,
  Box,
  FileInputItem,
  LoadingIndicator,
  Text,
  VideoCard,
  Placeholder,
} from "@canva/app-ui-kit";
import { FormattedMessage } from "react-intl";
import * as styles from "styles/generate.css";
import { useVideoStore, VideoSize } from "../stores/videoStore";
import { DragUploadVideo } from "./DragUploadVideo";
import { useIntl } from "react-intl";
import { formatDuration } from "../common/util";
import { If } from "./If";

// Video display states
enum VideoDisplayState {
  LOADING = "loading",
  NO_VIDEO = "no_video",
  UPLOAD_VIDEO = "upload_video",
  SELECTION_VIDEO = "selection_video",
}

export function VideoUploader() {
  const intl = useIntl();
  const {
    videoSrc,
    videoUrl,
    videoLoading,
    videoFromType,
    fileName,
    uploadTip,
    thumbnailUrl,
    duration,
    resolution,
    videoSize,
    setFileName,
    setUploadTip,
    setVideoMimeType,
    setVideoSrc,
    setVideoFromType,
    hideUploadTip,
    setVideoInfo,
    setVideoSize,
  } = useVideoStore();

  const [videoBoxHeight, setVideoBoxHeight] = useState(200);
  const [videoWidth, setVideoWidth] = useState(300);

  // Determine current video display state
  const getVideoDisplayState = (): VideoDisplayState => {
    if (videoLoading) return VideoDisplayState.LOADING;

    if (videoFromType === "selection" && videoUrl) {
      return VideoDisplayState.SELECTION_VIDEO;
    }

    if (videoFromType === "upload" && videoSrc) {
      return VideoDisplayState.UPLOAD_VIDEO;
    }

    return VideoDisplayState.NO_VIDEO;
  };

  const videoDisplayState = getVideoDisplayState();

  useEffect(() => {
    if (videoSrc) {
      computeVideoSize(videoSize);
    }
  }, [videoSize, videoSrc]);

  const computeVideoSize = (videoSize: VideoSize) => {
    const videoBox = document.getElementById("generateContainer");

    if (videoBox) {
      const width = videoBox.clientWidth;
      let height = (width / videoSize.width) * videoSize.height;
      if (height > 200) {
        height = 200;
      }
      const realW = (height / videoSize.height) * videoSize.width;
      setVideoBoxHeight(height);
      setVideoWidth(realW);
    }
  };

  const handleRemoveVideo = () => {
    setVideoSrc(null);
    setFileName("");
    setVideoMimeType("");
    setVideoFromType(null);
  };

  // Sub-components
  const UploadTipAlert = () => (
    <Box paddingTop="1u">
      <Alert
        tone={uploadTip.tone}
        title={uploadTip.title}
        onDismiss={hideUploadTip}
      >
        {uploadTip.desc}
      </Alert>
    </Box>
  );

  const VideoInstructions = () => (
    <Box paddingTop="1u">
      <Text size="medium">
        <FormattedMessage
          defaultMessage="Upload a video or select one in your design to enhance its quality"
          description="A title to inform the user how to start the video upload process."
        />
      </Text>
    </Box>
  );

  const VideoTitle = () => (
    <div className={styles.title}>
      <Text size="medium" variant="bold" tone="primary">
        <FormattedMessage
          defaultMessage="Original video"
          description="Original video"
        />
      </Text>
    </div>
  );

  const VideoLoadingIndicator = () => (
    <div className={styles.imgLoadingBox}>
      <LoadingIndicator size="medium" />
    </div>
  );

  const VideoUploadArea = () => (
    <div className={styles.simpleUploadContainer}>
      <DragUploadVideo
        videoSrc={videoSrc}
        setVideoInfo={setVideoInfo}
        fileName={fileName}
        setFileName={setFileName}
        setUploadTip={setUploadTip}
        setVideoMimeType={setVideoMimeType}
        setVideoSize={setVideoSize}
      />
      <Box width="full" height="full">
        <Text size="small" tone="tertiary">
          <FormattedMessage
            defaultMessage="Maximum video size: 1920 x 1080 or 30MB."
            description="Video upload constraint for maximum video size"
          />
        </Text>
        <Text size="small" tone="tertiary">
          <FormattedMessage
            defaultMessage="Maximum video duration: 60 seconds."
            description="Video upload constraint for maximum video duration"
          />
        </Text>
        <Text size="small" tone="tertiary">
          <FormattedMessage
            defaultMessage="Maximum frame rate: 60FPS."
            description="Video upload constraint for maximum frame rate"
          />
        </Text>
        <Text size="small" tone="tertiary">
          <FormattedMessage
            defaultMessage="Accepted formats: MP4, MOV, MKV."
            description="Video upload constraint for accepted file formats"
          />
        </Text>
      </Box>
    </div>
  );

  const UploadVideoPreview = () => (
    <Box borderRadius="large" className={styles.videoPreviewWrapper}>
      <Box
        id="videoBox-preview"
        background="neutralLow"
        borderRadius="large"
        className={styles.videoPreviewContainer}
      >
        <div
          style={{
            height: videoBoxHeight + "px",
            width: videoWidth + "px",
          }}
        >
          <Box width="full" height="full" borderRadius="standard">
            <Placeholder shape="sharpRectangle" />
          </Box>
        </div>
        <div
          style={{
            height: videoBoxHeight + "px",
            width: videoWidth + "px",
            position: "absolute",
            left: 0,
            right: 0,
            top: 0,
            bottom: 0,
            margin: "auto",
          }}
        >
          <Box width="full" height="full" borderRadius="standard">
            <VideoCard
              ariaLabel={intl.formatMessage({
                defaultMessage: "Original video",
                description: "Label for the original video",
              })}
              borderRadius="standard"
              mimeType="video/mp4"
              onClick={() => {}}
              onDragStart={() => {}}
              durationInSeconds={Number(formatDuration(duration))}
              thumbnailHeight={200}
              thumbnailUrl={thumbnailUrl}
              videoPreviewUrl={videoSrc || ""}
            />
          </Box>
        </div>
      </Box>
      <Box width="full" height="full">
        <FileInputItem label={fileName} onDeleteClick={handleRemoveVideo} />
        <Text size="medium" variant="regular">
          <FormattedMessage
            defaultMessage="Current video resolution"
            description="Label to display the current video's resolution"
          />
          • {resolution || "360p"}
        </Text>
      </Box>
    </Box>
  );

  const SelectionVideoPreview = () => (
    <Box borderRadius="large" className={styles.videoPreviewWrapper}>
      <Box
        id="videoBox-preview"
        background="neutralLow"
        borderRadius="large"
        className={styles.videoPreviewContainer}
      >
        <div
          style={{
            height: videoBoxHeight + "px",
            width: videoWidth + "px",
          }}
        >
          <Box width="full" height="full" borderRadius="standard">
            <Placeholder shape="sharpRectangle" />
          </Box>
        </div>
        <div
          style={{
            height: videoBoxHeight + "px",
            width: videoWidth + "px",
            position: "absolute",
            left: 0,
            right: 0,
            top: 0,
            bottom: 0,
            margin: "auto",
          }}
        >
          <Box width="full" height="full" borderRadius="standard">
            <VideoCard
              ariaLabel={intl.formatMessage({
                defaultMessage: "Selected video",
                description: "Label for the selected video",
              })}
              borderRadius="standard"
              mimeType="video/mp4"
              onClick={() => {}}
              onDragStart={() => {}}
              durationInSeconds={Number(formatDuration(duration))}
              thumbnailHeight={200}
              thumbnailUrl={thumbnailUrl}
              videoPreviewUrl={videoUrl || ""}
            />
          </Box>
        </div>
      </Box>
      <Box width="full" height="full">
        <FileInputItem
          label={fileName || "Selected video"}
          onDeleteClick={handleRemoveVideo}
        />
        <Text size="medium" variant="regular">
          <FormattedMessage
            defaultMessage="Current video resolution"
            description="Label to display the current video's resolution"
          />
          • {resolution || "360p"}
        </Text>
      </Box>
    </Box>
  );

  return (
    <>
      <If condition={uploadTip.title}>
        <UploadTipAlert />
      </If>

      <VideoInstructions />
      <VideoTitle />

      <If condition={videoDisplayState === VideoDisplayState.LOADING}>
        <VideoLoadingIndicator />
      </If>

      <If condition={videoDisplayState === VideoDisplayState.NO_VIDEO}>
        <VideoUploadArea />
      </If>

      <If condition={videoDisplayState === VideoDisplayState.UPLOAD_VIDEO}>
        <UploadVideoPreview />
      </If>

      <If condition={videoDisplayState === VideoDisplayState.SELECTION_VIDEO}>
        <SelectionVideoPreview />
      </If>
    </>
  );
}
              <Box
                id="videoBox-preview"
                background="neutralLow"
                borderRadius="large"
                className={styles.videoPreviewContainer}
              >
                <div
                  style={{
                    height: videoBoxHeight + "px",
                    width: videoWidth + "px",
                  }}
                >
                  <Box width="full" height="full" borderRadius="standard">
                    <Placeholder shape="sharpRectangle" />
                  </Box>
                </div>
                <div
                  style={{
                    height: videoBoxHeight + "px",
                    width: videoWidth + "px",
                    position: "absolute",
                    left: 0,
                    right: 0,
                    top: 0,
                    bottom: 0,
                    margin: "auto",
                  }}
                >
                  <Box width="full" height="full" borderRadius="standard">
                    <VideoCard
                      ariaLabel={intl.formatMessage({
                        defaultMessage: "Original video",
                        description: "Label for the original video",
                      })}
                      borderRadius="standard"
                      mimeType="video/mp4"
                      onClick={() => {}}
                      onDragStart={() => {}}
                      durationInSeconds={Number(formatDuration(duration))}
                      thumbnailHeight={200}
                      thumbnailUrl={thumbnailUrl}
                      videoPreviewUrl={videoSrc || ""}
                    />
                  </Box>
                </div>
              </Box>
              <Box width="full" height="full">
                <FileInputItem
                  label={fileName}
                  onDeleteClick={handleRemoveVideo}
                />
                <Text size="medium" variant="regular">
                  <FormattedMessage
                    defaultMessage="Current resolution"
                    description="Label to display the current video's resolution"
                  />
                  • {resolution || "360p"}
                </Text>
              </Box>
            </Box>
          )}
        </>
      )}

      {videoLoading && (
        <div className={styles.imgLoadingBox}>
          <LoadingIndicator size="medium" />
        </div>
      )}
    </>
  );
}
