.dragUploadBox {
  width: 100%;
  height: auto;
  height: 185px;
  border-radius: 8px;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  color: #fff;
  position: relative;
  overflow: hidden;
  /* background-color: var(--ui-kit-color-background-1); */
  /* background-color: rgba(255, 255 , 255, 0.07); */
  background-color: var(--ui-kit-color-neutral);
  background-repeat: no-repeat;
  background-size: contain;
  background-position: center;
}
.userUploadImage {
  position: relative;
}
.userUploadImage .upImg {
  height: auto;
  width: auto;
  max-width: calc(100vw - 64px);
  max-height: 185px;
  pointer-events: none;
}
.userUploadImage .delete {
  width: 24px;
  height: 24px;
  position: absolute;
  top: -8px;
  right: -8px;
  cursor: pointer;
}

.uploadImage {
  width: 54px;
  height: 46px;
  position: relative;
  z-index: 5;
}
