import * as styles from "./drag_uplaod_image.css";
import * as React from "react";
import { FileInput, FileInputItem, Text, Rows } from "@canva/app-ui-kit";
import { imageToBase64 } from "utils/tools";
import { useIntl } from "react-intl";

export const GragUploadImage = ({
  imgSrc,
  setImgInfo,
  fileName,
  setFileName,
  setUploadTip,
  setImgMimeType,
}) => {
  const intl = useIntl();
  const [refeshKey, setRefeshKey] = React.useState(0);

  const handleDrop = (e: React.DragEvent) => {
    if (e.dataTransfer.files) {
      const imgFile: File = e.dataTransfer.files[0];
      handleFile(imgFile);
    }
  };

  const handleFile = (imgFile: File) => {
    try {
      // console.log("++++++++", imgFile);
      const size = imgFile.size;
      const type = imgFile.type;
      const types = [
        "image/png",
        "image/jpeg",
        "image/jpg",
        "image/heic",
        "image/webp",
      ];
      setRefeshKey(refeshKey + 1);
      setUploadTip({
        title: "",
        desc: "",
        tone: "critical",
      });
      if (types.includes(type)) {
        const fileReader = new FileReader();
        fileReader.readAsDataURL(imgFile);
        fileReader.onload = (e) => {
          if (e.target && e.target.result) {
            var img = new Image();
            img.src = e.target.result as string;
            img.onload = () => {
              if (size > 1024 * 1024 * 6) {
                // setShowUploadTip({
                //   title: "That file is too large.",
                //   desc: " Please choose one that’s smaller than 6MB.",
                //   tone: "critical",
                // });
                setUploadTip({
                  title: intl.formatMessage({
                    defaultMessage: "That file is too large.",
                    description:
                      "An error message when file size uploaded is too large.",
                  }),
                  desc: intl.formatMessage({
                    defaultMessage:
                      "Please choose one that’s smaller than 6MB.",
                    description:
                      "An error message when file size uploaded is too large.",
                  }),
                  tone: "critical",
                });
              } else {
                if (size <= 1024 * 1024 * 2) {
                  const imgSize = {
                    width: img.width,
                    height: img.height,
                  };
                  console.log("lwq - imgSize", imgSize);
                  setImgInfo({
                    imgSrc: e.target?.result as string,
                    imgSize: imgSize,
                    fromType: "upload",
                  });
                } else {
                  const obj = imageToBase64(img, type);
                  if (obj.imgBase64) {
                    setImgInfo({
                      imgSrc: obj.imgBase64 as string,
                      imgSize: {
                        width: obj.width,
                        height: obj.height,
                      },
                      fromType: "upload",
                    });
                  } else {
                    // setShowUploadTip({
                    //   title: "Image corrupted. ",
                    //   desc: "Try a different one?",
                    //   tone: "critical",
                    // });
                    setUploadTip({
                      title: intl.formatMessage({
                        defaultMessage: "Image corrupted.",
                        description: "An error message.",
                      }),
                      desc: intl.formatMessage({
                        defaultMessage: "Try a different one.",
                        description: "An error message.",
                      }),
                      tone: "critical",
                    });
                  }
                }
              }

              setFileName(`${imgFile.name}`);
              setImgMimeType(type);
            };
          }
        };
      } else {
        setUploadTip({
          title: intl.formatMessage({
            defaultMessage:
              "Speed Painter currently doesn’t support this element.",
            description:
              "An error message when user selected the unsupported element from design.",
          }),
          desc: intl.formatMessage({
            defaultMessage: "Please try an image.",
            description:
              "An error message when user selected the unsupported element from design.",
          }),
          tone: "critical",
        });
      }
    } catch (error) {
      console.error("+++++++++", error);
      // setShowUploadTip({
      //   title: "Image corrupted. ",
      //   desc: "Try a different one?",
      //   tone: "critical",
      // });
      setUploadTip({
        title: intl.formatMessage({
          defaultMessage: "Image corrupted.",
          description: "An error message.",
        }),
        desc: intl.formatMessage({
          defaultMessage: "Try a different one.",
          description: "An error message.",
        }),
        tone: "critical",
      });
    }
  };

  const handleDelete = () => {
    setImgInfo({
      imgSrc: null,
      imgSize: 0,
      fromType: "",
    });
    setFileName("");
  };

  if (imgSrc) {
    return (
      <Rows spacing="1u">
        <div
          className={styles.dragUploadBox}
          onDrop={handleDrop}
          style={{ backgroundImage: `url(${imgSrc})` }}
        ></div>
        <FileInputItem label={fileName} onDeleteClick={handleDelete} />
      </Rows>
    );
  }

  return (
    <Rows spacing="1u">
      <FileInput
        multiple={false}
        accept={["image/png", "image/jpeg", "image/jpg", "image/webp"]}
        onDropAcceptedFiles={(files: File[]) => handleFile(files[0])}
        stretchButton
      ></FileInput>
    </Rows>
  );
};
