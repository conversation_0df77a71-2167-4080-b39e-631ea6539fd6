import { Button } from "@canva/app-ui-kit";
import { TouchOrMouseEvent } from "@canva/app-ui-kit/dist/cjs/ui/apps/developing/ui_kit/components/button/button";

import { useIntl } from "react-intl";
import { useAppStore } from "src/stores/appStore";

interface SignInToGenerateButtonProps {
  onClick: (event: TouchOrMouseEvent<any>) => void;
}

export function SignInToGenerateButton({
  onClick,
}: SignInToGenerateButtonProps) {
  const intl = useIntl();
  const buttonLoading = useAppStore((state) => state.buttonLoading);

  return (
    <Button variant="primary" stretch loading={buttonLoading} onClick={onClick}>
      {intl.formatMessage({
        defaultMessage: "Sign in to generate",
        description: "Generate button",
      })}
    </Button>
  );
}
