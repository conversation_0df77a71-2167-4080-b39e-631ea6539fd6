.videoPreviewContainer {
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  padding: 16px;
  border: 2px dashed #e0e0e0;
  transition: all 0.2s ease;
}

.videoPreviewContainer:hover {
  border-color: var(--ui-kit-color-primary-hover);
  background-color: #f5f5f5;
}

.videoPreview {
  width: 100%;
  display: block;
  border-radius: 8px;
}

.videoInfo {
  padding-top: 12px;
  background-color: white;
}

.removeButton {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: rgba(255, 0, 0, 0.7);
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.2s;
}

.removeButton:hover {
  background-color: rgba(255, 0, 0, 0.9);
}

.videoInput {
  display: none;
}
