import * as React from "react";
import { FileInput, FileInputItem, Rows } from "@canva/app-ui-kit";
import { useIntl } from "react-intl";
import { VideoInfo } from "../../types/generate";
import * as styles from "./index.css";
import { UploadTip } from "../../stores/videoStore";
import { formatFileSize } from "../../common/util";
import {
  SUPPORTED_VIDEO_TYPES,
  VideoMaxSize,
  VideoMaxDuration,
} from "../../common/constants";
import { getVideoResolution } from "../../common/util";
import { VideoSize } from "../../stores/videoStore";

type DragUploadVideoProps = {
  videoSrc: string | null;
  setVideoInfo: (info: VideoInfo) => void;
  fileName: string;
  setFileName: (name: string) => void;
  setUploadTip: (tip: UploadTip) => void;
  setVideoMimeType: (type: string) => void;
  setVideoSize: (videoSize: VideoSize) => void;
};

export const DragUploadVideo = ({
  videoSrc,
  setVideoInfo,
  fileName,
  setFileName,
  setUploadTip,
  setVideoMimeType,
  setVideoSize,
}: DragUploadVideoProps) => {
  const intl = useIntl();
  const [refreshKey, setRefreshKey] = React.useState(0);

  const handleFile = async (videoFile: File) => {
    try {
      const size = videoFile.size;
      const type = videoFile.type;

      setRefreshKey(refreshKey + 1);
      setUploadTip({
        title: "",
        desc: "",
        tone: "critical",
      });

      if (!SUPPORTED_VIDEO_TYPES.includes(type)) {
        setUploadTip({
          title: intl.formatMessage({
            defaultMessage: "Invalid file format",
            description: "Error message for invalid video format",
          }),
          desc: intl.formatMessage({
            defaultMessage: "Please upload MP4, MOV, or MKV files only.",
            description: "Error message details for invalid video format",
          }),
          tone: "critical",
        });
        return;
      }

      if (size > VideoMaxSize) {
        setUploadTip({
          title: intl.formatMessage({
            defaultMessage: "File too large",
            description: "Error message for large file size",
          }),
          desc: intl.formatMessage({
            defaultMessage: `Maximum file size is ${formatFileSize(VideoMaxSize)}`,
            description: "Error message details for large file size",
          }),
          tone: "critical",
        });
        return;
      }

      // Create video element to check duration and codec
      const videoElement = document.createElement("video");
      videoElement.preload = "metadata";

      const objectUrl = URL.createObjectURL(videoFile);
      videoElement.src = objectUrl;

      videoElement.onloadedmetadata = () => {
        URL.revokeObjectURL(objectUrl);

        // Check duration
        if (videoElement.duration > VideoMaxDuration) {
          setUploadTip({
            title: intl.formatMessage({
              defaultMessage: "Video too long",
              description: "Error message for long video",
            }),
            desc: intl.formatMessage({
              defaultMessage: `Maximum video duration is ${VideoMaxDuration} seconds.`,
              description: "Error message details for long video",
            }),
            tone: "critical",
          });
          return;
        }

        setFileName(videoFile.name);
        setVideoMimeType(type);

        const reader = new FileReader();
        reader.onload = (event) => {
          if (event.target && event.target.result) {
            const src = event.target.result as string;
            const resolution = getVideoResolution(videoElement);

            const info: VideoInfo = {
              src,
              name: videoFile.name,
              type: videoFile.type,
              size: videoFile.size,
              duration: videoElement.duration,
              url: null,
              resolution,
            };

            console.log("info", { info, videoElement });
            setVideoSize({
              width: videoElement.videoWidth,
              height: videoElement.videoHeight,
            });
            setVideoInfo(info);
          }
        };
        reader.readAsDataURL(videoFile);
      };

      videoElement.onerror = () => {
        URL.revokeObjectURL(objectUrl);
        setUploadTip({
          title: intl.formatMessage({
            defaultMessage: "Invalid video",
            description: "Error message for invalid video",
          }),
          desc: intl.formatMessage({
            defaultMessage: "The video must use H.264 encoding.",
            description: "Error message details for invalid video encoding",
          }),
          tone: "critical",
        });
      };
    } catch (error) {
      console.error("Error processing video:", error);
      setUploadTip({
        title: intl.formatMessage({
          defaultMessage: "Video processing failed",
          description: "Error message for video processing failure",
        }),
        desc: intl.formatMessage({
          defaultMessage: "Try a different video file.",
          description: "Error message suggestion for video processing failure",
        }),
        tone: "critical",
      });
    }
  };

  const handleDelete = () => {
    setVideoInfo({
      src: "",
      name: "",
      type: "",
      size: 0,
      duration: 0,
      url: null,
    });
    setFileName("");
  };

  if (videoSrc) {
    return (
      <Rows spacing="1u">
        <div className={styles.videoPreviewContainer}>
          <video src={videoSrc} controls className={styles.videoPreview} />
        </div>
        <FileInputItem label={fileName} onDeleteClick={handleDelete} />
      </Rows>
    );
  }

  return (
    <Rows spacing="1u">
      <FileInput
        multiple={false}
        accept={SUPPORTED_VIDEO_TYPES}
        onDropAcceptedFiles={(files: File[]) => handleFile(files[0])}
        stretchButton
      />
    </Rows>
  );
};
