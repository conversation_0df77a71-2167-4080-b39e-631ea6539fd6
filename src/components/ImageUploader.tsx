// src/components/ImageUploader.tsx
import { Al<PERSON>, Box, LoadingIndicator, Text, Title } from "@canva/app-ui-kit";
import * as React from "react";
import { FormattedMessage } from "react-intl";
import * as styles from "styles/generate.css";
import { GragUploadImage } from "./drag_upload_image";
import { useImageStore } from "../stores/imageStore";
import { ImageInfo } from "../types/generate";

interface ImageUploaderProps {
  setImgInfo: (info: ImageInfo) => void;
}

export function ImageUploader({ setImgInfo }: ImageUploaderProps) {
  const {
    imgSrc,
    imgLoading,
    imgFromType,
    fileName,
    uploadTip,
    setFileName,
    setUploadTip,
    setImgMimeType,
  } = useImageStore();

  const tipHide = () => {
    setUploadTip({
      title: "",
      desc: "",
      tone: "critical",
    });
  };

  return (
    <>
      {uploadTip.title ? (
        <Box paddingTop="1u">
          <Alert
            tone={uploadTip.tone}
            title={uploadTip.title}
            onDismiss={tipHide}
          >
            {uploadTip.desc}
          </Alert>
        </Box>
      ) : null}

      {!imgSrc && (
        <Box paddingTop="1u">
          <Text size="medium">
            <FormattedMessage
              defaultMessage="Upload an image or select one in your design to create a speed painting video"
              description="A title to inform the user how to start the generation process."
            />
          </Text>
        </Box>
      )}

      <div className={styles.title}>
        <Text size="medium" variant="bold" tone="primary">
          <FormattedMessage
            defaultMessage="Original image"
            description="Original image"
          />
        </Text>
      </div>

      {!imgLoading &&
        imgFromType !== "selection" &&
        imgFromType !== "selection-base64" && (
          <GragUploadImage
            imgSrc={imgSrc}
            setImgInfo={setImgInfo}
            fileName={fileName}
            setFileName={setFileName}
            setUploadTip={setUploadTip}
            setImgMimeType={setImgMimeType}
          />
        )}

      {imgSrc ? (
        (imgFromType === "selection" || imgFromType === "selection-base64") && (
          <div
            className={styles.dragUploadBox}
            style={{ backgroundImage: `url(${imgSrc})` }}
          />
        )
      ) : imgLoading ? (
        <div className={styles.imgLoadingBox}>
          <LoadingIndicator size="medium" />
        </div>
      ) : (
        <Text size="small" tone="tertiary">
          <FormattedMessage
            defaultMessage="Maximum file size: 6MB. Accepted file formats: JPEG, JPG, PNG, WEBP."
            description="Information about the maximum file size and accepted file formats."
          />
        </Text>
      )}
    </>
  );
}
