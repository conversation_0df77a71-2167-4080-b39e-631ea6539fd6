import React from "react";
import { Box, Placeholder } from "@canva/app-ui-kit";
import sliderLine from "assets/images/line2.png";

import * as styles from "./styles.css";
import { FormattedMessage } from "react-intl";

let moveX: number = 0;
let startX: number = 0;
let animationId: number | undefined;
let compareBoxWidth: number = 0;
let video1: HTMLVideoElement | null = null;
let video2: HTMLVideoElement | null = null;

export default function CompareVideo({ oldVideoSrc, newVideoUrl }) {
  const [compareScale, setCompareScale] = React.useState<number>(50);
  const [boxwidth, setBoxwidth] = React.useState(327);
  const [canTouch, setCanTouch] = React.useState(true);
  const canplay = React.useRef(false);
  const [canClickPlay, setCanClickPlay] = React.useState(false);
  const isPlaying = React.useRef(false);
  const clickPlaying = React.useRef(false);
  const [clickPlay, setClickPlay] = React.useState(false);
  const canClickNum = React.useRef(0);

  React.useEffect(() => {
    if ("ontouchstart" in window || navigator.maxTouchPoints > 0) {
      // 设备支持触摸
      setCanTouch(true);
    } else {
      // 设备不支持触摸
      setCanTouch(false);
    }
    if (document.getElementById("compareBox")) {
      compareBoxWidth = document.getElementById("compareBox")
        ?.clientWidth as number;
      // console.log('+++++++compareBoxWidth2', compareBoxWidth);
      setBoxwidth(compareBoxWidth);
    }
    const oldVideo = document.getElementById("oldVideo") as HTMLVideoElement;
    const newVideo = document.getElementById("newVideo") as HTMLVideoElement;

    const canplaythroughListen = () => {
      if (!canplay.current) {
        // console.log("+++****")
        canClickNum.current = canClickNum.current + 1;
        video1 = document.getElementById("oldVideo") as HTMLVideoElement;
        video2 = document.getElementById("newVideo") as HTMLVideoElement;
        newVideo.removeEventListener("canplaythrough", canplaythroughListen);
        if (canClickNum.current > 1) {
          canplay.current = true;
          setCanClickPlay(true);
          clickPlayVideo();
        }
      }
    };

    const canplaythroughListen_old = () => {
      if (!canplay.current) {
        canClickNum.current = canClickNum.current + 1;
        // console.log("+++****")
        video1 = document.getElementById("oldVideo") as HTMLVideoElement;
        video2 = document.getElementById("newVideo") as HTMLVideoElement;
        newVideo.removeEventListener(
          "canplaythrough",
          canplaythroughListen_old
        );
        // playVideo();
        if (canClickNum.current > 1) {
          canplay.current = true;
          setCanClickPlay(true);
          clickPlayVideo();
        }
      }
    };

    if (oldVideo && newVideo) {
      // console.log("++++1")
      oldVideo.addEventListener("canplaythrough", canplaythroughListen_old);
      newVideo.addEventListener("canplaythrough", canplaythroughListen);
    } else {
      // console.log("++++2")
      setTimeout(() => {
        const oldVideo2 = document.getElementById(
          "oldVideo"
        ) as HTMLVideoElement;
        const newVideo2 = document.getElementById(
          "newVideo"
        ) as HTMLVideoElement;
        if (oldVideo2 && newVideo2) {
          // console.log("+++****")
          oldVideo.addEventListener("canplaythrough", canplaythroughListen_old);
          newVideo2.addEventListener("canplaythrough", canplaythroughListen);
        }
      }, 2000);
    }
  }, []);

  const playVideo = () => {
    if (canplay.current && !isPlaying.current && clickPlaying.current) {
      if (video1 && video2) {
        video2.play();
        isPlaying.current = true;
        // Function to sync the second video with the first video
        function syncVideos() {
          if (video1 && video2) {
            if (!video2.paused) {
              video1.currentTime = video2.currentTime;
            }
          }
        }

        video2.addEventListener("play", function () {
          try {
            video1 && video1.play();
          } catch (error) {
            console.error(error);
          }
        });

        video2.addEventListener("pause", function () {
          try {
            if (video1) {
              video1.pause();
            }
          } catch (error) {
            console.error(error);
          }
        });

        video2.addEventListener("seeked", syncVideos);

        video2.addEventListener("timeupdate", function () {
          try {
            if (video1 && video2) {
              if (Math.abs(video2.currentTime - video1.currentTime) > 0.05) {
                video1.currentTime = video2.currentTime;
              }
            }
          } catch (error) {
            console.error(error);
          }
        });
      }
    }
  };

  const clickPlayVideo = () => {
    clickPlaying.current = true;
    setClickPlay(true);
    playVideo();
  };

  const handleSliderDown = (
    e: React.MouseEvent<HTMLImageElement, MouseEvent>
  ) => {
    e.preventDefault();
    e.stopPropagation();
    startX = e.clientX; //鼠标距离可视区域的宽
    moveX = 0;
    if (document.getElementById("compareBox") && compareBoxWidth == 0) {
      compareBoxWidth = document.getElementById("compareBox")
        ?.clientWidth as number;
      // console.log('+++++++compareBoxWidth', compareBoxWidth);
      setBoxwidth(compareBoxWidth);
    }
    if (video2) {
      video2.pause();
    }
    // console.log("compareBoxWidth", compareBoxWidth);
    window.addEventListener("mousemove", onMousemove);
    window.addEventListener("mouseup", onMouseup);
  };

  const onMousemove = (event: MouseEvent) => {
    event.preventDefault();
    event.stopPropagation();
    if (animationId) {
      window.cancelAnimationFrame(animationId);
    }
    animationId = window.requestAnimationFrame(() => {
      moveX = event.clientX - startX;
      if (startX !== 0) {
        // console.log("moveX", moveX, event.clientX, startX);
        const scale = (moveX / compareBoxWidth) * 100;
        if (compareScale - scale > 100) {
          setCompareScale(100);
        } else if (compareScale - scale < 0) {
          setCompareScale(0);
        } else {
          setCompareScale(compareScale - scale);
        }
      }
    });
  };

  const onMouseup = () => {
    moveX = 0;
    startX = 0;
    animationId = undefined;
    window.removeEventListener("mousemove", onMousemove);
    window.removeEventListener("onMouseup", onMouseup);
    if (canplay.current && clickPlaying.current) {
      if (video2) {
        video2.play();
      } else {
        canplay.current = true;
        video1 = document.getElementById("oldVideo") as HTMLVideoElement;
        video2 = document.getElementById("newVideo") as HTMLVideoElement;
        playVideo();
      }
    }
  };

  const handleSliderDown_touch = (e: React.TouchEvent<HTMLImageElement>) => {
    e.preventDefault();
    e.stopPropagation();
    startX = e.touches[0].clientX; //e.clientX; //鼠标距离可视区域的宽
    moveX = 0;
    if (document.getElementById("compareBox") && compareBoxWidth == 0) {
      compareBoxWidth = document.getElementById("compareBox")
        ?.clientWidth as number;
      // console.log('+++++++compareBoxWidth', compareBoxWidth);
      setBoxwidth(compareBoxWidth);
    }
    video2 && video2.pause();
    // console.log("compareBoxWidth", compareBoxWidth);
    window.addEventListener("touchmove", onMousemove_touch);
    window.addEventListener("touchend", onMouseup_touch);
  };

  const onMousemove_touch = (event: TouchEvent) => {
    event.preventDefault();
    event.stopPropagation();
    if (animationId) {
      window.cancelAnimationFrame(animationId);
    }
    animationId = window.requestAnimationFrame(() => {
      moveX = event.touches[0].clientX - startX; //event.clientX - startX;
      if (startX !== 0) {
        // console.log("moveX", moveX, event.clientX, startX);
        const scale = (moveX / compareBoxWidth) * 100;
        if (compareScale - scale > 100) {
          setCompareScale(100);
        } else if (compareScale - scale < 0) {
          setCompareScale(0);
        } else {
          setCompareScale(compareScale - scale);
        }
      }
    });
  };

  const onMouseup_touch = () => {
    moveX = 0;
    startX = 0;
    animationId = undefined;
    window.removeEventListener("touchmove", onMousemove_touch);
    window.removeEventListener("touchend", onMouseup_touch);
    if (canplay.current && clickPlaying.current) {
      if (video2) {
        video2.play();
      } else {
        canplay.current = true;
        video1 = document.getElementById("oldVideo") as HTMLVideoElement;
        video2 = document.getElementById("newVideo") as HTMLVideoElement;
        playVideo();
      }
    }
  };

  const onMouseLeave = () => {
    if (canTouch) {
      onMouseup_touch();
    } else {
      onMouseup();
    }
  };

  return (
    <div
      className={styles.compareBox}
      id="compareBox"
      onMouseLeave={onMouseLeave}
    >
      <div
        className={styles.userOldImg}
        style={{ display: canClickPlay ? "block" : "none" }}
      >
        <video
          src={oldVideoSrc}
          id="oldVideo"
          style={{ width: "100%", height: "100%" }}
          preload="auto"
          loop
          muted
        ></video>
      </div>
      <div
        className={styles.upScaleImg2}
        style={{
          width: `${compareScale}%`,
          overflow: "hidden",
          display: canClickPlay ? "block" : "none",
        }}
      >
        <video
          src={newVideoUrl}
          id="newVideo"
          preload="auto"
          loop
          muted
          style={{
            height: boxwidth + "px",
            width: boxwidth + "px",
            position: "absolute",
            top: 0,
            right: 0,
          }}
        ></video>
      </div>

      {canClickPlay ? <></> : <Placeholder shape="sharpRectangle" />}

      {!clickPlay ? (
        <></>
      ) : canTouch ? (
        <img
          alt="sliderLine"
          src={sliderLine}
          className={`${styles.sliderLine} ${styles.sliderLinePosition}`}
          draggable="false"
          style={{ right: `${compareScale}%` }}
          onTouchStart={handleSliderDown_touch}
        />
      ) : (
        <img
          alt="sliderLine"
          src={sliderLine}
          className={`${styles.sliderLine} ${styles.sliderLinePosition}`}
          draggable="false"
          style={{ right: `${compareScale}%` }}
          onMouseDown={handleSliderDown}
        />
      )}

      {canClickPlay ? (
        <>
          <div className={styles.before}>
            <Box
              background="contrast"
              padding="0.5u"
              width="unset"
              height="unset"
              borderRadius="standard"
            >
              <FormattedMessage
                defaultMessage="Before"
                description="Labe for the input as 'Before'"
              />
            </Box>
          </div>
          <div className={styles.after}>
            <Box
              background="contrast"
              padding="0.5u"
              width="unset"
              height="unset"
              borderRadius="standard"
            >
              <FormattedMessage
                defaultMessage="After"
                description="Lable for the output as 'After'"
              />
            </Box>
          </div>
        </>
      ) : (
        <></>
      )}
    </div>
  );
}
