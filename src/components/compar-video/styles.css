.compareBox {
  width: calc(100vw - 32px);
  height: calc(100vw - 32px);
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  background-color: var(--ui-kit-color-neutral-low);
}

.playBtn {
  background-color: var(--ui-kit-color-neutral);
}

.compareBox .oldImg {
  width: 100%;
  height: 100%;
  position: relative;
  z-index: 10;
  pointer-events: none;
}

.compareBox .userOldImg {
  position: relative;
  z-index: 10;
  width: 100%;
  height: 100%;
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  pointer-events: none;
}

.compareBox .upScaleImg2 {
  width: 50%;
  height: 100%;
  position: absolute;
  z-index: 20;
  top: 0;
  right: 0;
}

.compareBox .upScaleImg {
  width: 50%;
  height: 100%;
  position: absolute;
  z-index: 20;
  top: 0;
  right: 0;
  background-size: auto 100%;
  background-position-y: top;
  background-repeat: no-repeat;
  pointer-events: none;
}

.compareBox .upScaleImgBox {
  position: absolute;
  z-index: 20;
  top: 0;
  right: 0;
  width: 100%;
  height: 100%;
}
.compareBox .upScaleImgBody {
  position: absolute;
  top: 0;
  right: 0;
  width: calc(100vw - 32px);
  height: 100%;
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
}

.sliderLine {
  position: absolute;
  z-index: 30;
  height: 100%;
  width: auto;
  right: 50%;
  transform: translateX(50%);
  top: 0;
  cursor: col-resize;
}
.before {
  position: absolute;
  z-index: 31;
  width: auto;
  top: 8px;
  left: 8px;
  /* background-color: var(--ui-kit-color-contrast);
  padding: 4px;
  border-radius: 4px; */
}
.after {
  position: absolute;
  z-index: 31;
  width: auto;
  top: 8px;
  right: 8px;
  /* background-color: var(--ui-kit-color-contrast);
  padding: 4px;
  border-radius: 4px; */
}

.oldVideo {
  width: 100%;
  height: 100%;
}

.newVideo {
  height: 100%;
  width: 100%;
  position: absolute;
  top: 0;
  right: 0;
}

.upScaleImgContainer {
  overflow: hidden;
}

.sliderLinePosition {
  position: absolute;
}

/* Add these new styles */
.displayBlock {
  display: block;
}

.displayNone {
  display: none;
}

.dynamicWidth {
  /* Base style, width will be set via className composition */
}

.sliderRight {
  /* Base style, right position will be set via className composition */
}
