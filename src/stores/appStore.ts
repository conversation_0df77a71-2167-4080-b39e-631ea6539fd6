import { create } from "zustand";
import { PageMode } from "../types/generate";


interface AppState {
  pageMode: PageMode;
  errorContent: string;
  errorTitle: string;
  buttonLoading: boolean;
  showCreditNotEnough: boolean;

  // actions
  resetAppState: () => void;
}

export const useAppStore = create<AppState>((set) => ({
  pageMode: "Generate",
  errorContent: "",
  errorTitle: "",
  buttonLoading: false,
  showCreditNotEnough: false,

  resetAppState: () =>
    set({
      pageMode: "Generate",
      errorContent: "",
      errorTitle: "",
      buttonLoading: false,
      showCreditNotEnough: false,
    }),
}));
