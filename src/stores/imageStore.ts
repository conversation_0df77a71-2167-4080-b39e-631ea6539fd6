// src/stores/imageStore.ts

import { Tone } from "@canva/app-ui-kit/dist/cjs/ui/apps/developing/ui_kit/components/alert/alert";
import { ImageFromType, ImageInfo, UploadTip } from "src/types/generate";
import { ImageSize } from "src/types/generate";
import { create } from "zustand";

interface ImageStore {
  // 图片状态
  imgFromType: ImageFromType;
  imgSrc: string | null;
  imgSize: ImageSize;
  imgUrl: string;
  imgMimeType: string;
  imgLoading: boolean;
  fileName: string;

  // 提示状态
  uploadTip: UploadTip;

  // Actions
  setImageInfo: (info: ImageInfo) => void;
  setImgLoading: (loading: boolean) => void;
  setUploadTip: (tip: UploadTip) => void;
  setFileName: (name: string) => void;
  setImgUrl: (url: string) => void;
  setImgMimeType: (type: string) => void;
  resetState: () => void;
}

const initialState = {
  imgFromType: "" as ImageFromType,
  imgSrc: null,
  imgSize: { width: 0, height: 0 },
  imgUrl: "",
  imgMimeType: "",
  imgLoading: false,
  fileName: "",
  uploadTip: {
    title: "",
    desc: "",
    tone: "critical" as Tone,
  },
};

export const useImageStore = create<ImageStore>((set) => ({
  ...initialState,

  setImageInfo: (info) =>
    set({
      imgSrc: info.imgSrc,
      imgSize: info.imgSize,
      imgFromType: info.fromType as ImageFromType,
    }),

  setImgLoading: (loading) => set({ imgLoading: loading }),
  setUploadTip: (tip) => set({ uploadTip: tip }),
  setFileName: (name) => set({ fileName: name }),
  setImgUrl: (url) => set({ imgUrl: url }),
  setImgMimeType: (type) => set({ imgMimeType: type }),
  resetState: () => set(initialState),
}));
