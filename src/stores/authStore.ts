import { create } from "zustand";
import { AccessTokenResponse, auth } from "@canva/user";
import { CreditClient, CreditRecord, exchangeToken } from "../lib";

import { getUserInfo, UserInfo } from "../lib/get-user";
import { baseUrl } from "../lib";

interface AuthState {
  accessToken: AccessTokenResponse | null;
  a1dToken: string | null;
  credit: CreditRecord | null;
  userInfo: UserInfo | null;
  loading: boolean;
  error: string | null;

  login: () => Promise<AccessTokenResponse>;
  logout: () => void;
  updateCredit: (credit: CreditRecord) => void;
  fetchUserInfo: (token: string) => Promise<void>;
  fetchUserCredit: (token: string) => Promise<void>;
  retrieveAndSetToken: () => Promise<AccessTokenResponse>;
}

const oauth = auth.initOauth();

export const useAuthStore = create<AuthState>((set, get) => ({
  accessToken: null,
  a1dToken: null,
  credit: null,
  userInfo: null,
  loading: false,
  error: null,

  fetchUserInfo: async (token: string) => {
    try {
      const response = await getUserInfo({ token, baseUrl });
      if (response.success && response.data) {
        set({ userInfo: response.data });
      }
    } catch (err) {
      console.error("Failed to fetch user info:", err);
    }
  },

  fetchUserCredit: async (token: string) => {
    try {
      const creditClient = new CreditClient({
        token,
        baseUrl,
      });
      const response = await creditClient.getUserCredit();
      if (response.success) {
        set({ credit: response.data });
      }
    } catch (err) {
      console.error("Failed to fetch user credit:", err);
    }
  },

  updateCredit: (credit: CreditRecord) => {
    set({ credit });
  },

  retrieveAndSetToken: async () => {
    try {
      set({ loading: true, error: null });
      const token = await oauth.getAccessToken();
      set({ accessToken: token });
      if (token) {
        const newToken = await exchangeToken({
          source: "web",
          token: token.token,
          baseUrl,
        });
        set({
          a1dToken: newToken,
        });

        if (newToken) {
          await Promise.all([
            get().fetchUserInfo(newToken),
            get().fetchUserCredit(newToken),
          ]);
        }
      }
      return token;
    } catch (err) {
      set({ error: "Failed to retrieve access token" });
      throw err;
    } finally {
      set({ loading: false });
    }
  },

  login: async () => {
    try {
      set({ loading: true, error: null });
      const authorizeResponse = await oauth.requestAuthorization();
      if (authorizeResponse.status === "completed") {
        return await get().retrieveAndSetToken();
      } else {
        throw new Error("Authorization not completed");
      }
    } catch (err) {
      set({ error: "Login failed" });
      throw err;
    } finally {
      set({ loading: false });
    }
  },

  logout: () => {
    oauth.deauthorize();
    set({
      accessToken: null,
      a1dToken: null,
      credit: null,
      userInfo: null,
    });

    // 清除所有持久化状态
    localStorage.removeItem("vu-task-storage");
  },
}));
