import { create } from "zustand";
import {
  persist,
  devtools,
  createJSONStorage,
  StateStorage,
} from "zustand/middleware";
import { TaskResult, VuGenerateData } from "../types/generate";
import * as api from "../lib";
import {
  TaskStatus,
  getTaskStatusStream,
  cancelTask,
  CreditClient,
  CreditResponse,
} from "../lib";
import { useAuthStore } from "./authStore";
import { useAppStore } from "./appStore";

interface TaskState {
  taskId: string;
  progress: number;
  abortController: AbortController | null;
  taskResult: TaskResult | null;
  is_cancelled: boolean;
  currentStep: "uploading" | "submitting" | "processing" | null;
  videoUrlToProcess: string | null;
  isCancelling: boolean; // 新增：取消过程中的状态

  // actions
  resetState: () => void;
  handleVuGenerate: (data: VuGenerateData) => Promise<void>;
  handleCancel: () => Promise<void>;
  retrieveProcessingTaskStatus: () => Promise<void>;
}

// ---- Custom Storage with Expiration ----
const EXPIRATION_DAYS = 14;
const EXPIRATION_MS = EXPIRATION_DAYS * 24 * 60 * 60 * 1000;

// Define structure for the stored value including expiration
type PersistedValue<S> = {
  state: S;
  version: number;
  expiresAt?: number; // Add expiration timestamp here
};

const storageWithExpiration: StateStorage = {
  getItem: (name) => {
    const str = localStorage.getItem(name);
    if (!str) return null;
    try {
      const storedValue: PersistedValue<Partial<TaskState>> = JSON.parse(str);
      // Check expiration
      if (storedValue.expiresAt && Date.now() > storedValue.expiresAt) {
        console.log(`Persisted state "${name}" expired, removing.`);
        localStorage.removeItem(name);
        return null; // Return null to indicate expired/missing data
      }
      console.log("Persisted state:", storedValue);
      // Return the full string if not expired, createJSONStorage expects this format
      return str;
    } catch (e) {
      console.error(`Error reading or parsing stored state "${name}":`, e);
      localStorage.removeItem(name); // Remove potentially corrupted data
      return null;
    }
  },
  setItem: (name, newValueString) => {
    // newValueString is already stringified by createJSONStorage
    // It looks like: '{"state":{...},"version":...}'
    try {
      const newValue: { state: Partial<TaskState>; version: number } =
        JSON.parse(newValueString);
      const expiresAt = Date.now() + EXPIRATION_MS;
      // Add expiresAt to the object before stringifying again for storage
      const valueToStore: PersistedValue<Partial<TaskState>> = {
        ...newValue,
        expiresAt: expiresAt,
      };
      localStorage.setItem(name, JSON.stringify(valueToStore));
    } catch (e) {
      console.error(`Error setting stored state "${name}" with expiration:`, e);
      // Optionally handle the error, e.g., store without expiration or log
    }
  },
  removeItem: (name) => localStorage.removeItem(name),
};
// ---- End Custom Storage ----

const defaultTaskState = {
  taskId: "",
  progress: 5,
  abortController: null,
  taskResult: null,
  is_cancelled: false,
  currentStep: null,
  videoUrlToProcess: null,
  isCancelling: false,
};

// const mockTask = {
//   taskId: "Yp8fFHQoix-0eMthwHtc6",
//   progress: 5,
//   abortController: null,
//   taskResult: {
//     sketchImageUrl:
//       "https://www.canva.dev/example-assets/video-import/beach-thumbnail-image.jpg",
//     videoUrl:
//       "https://www.canva.dev/example-assets/video-import/beach-thumbnail-video.mp4",
//     taskId: "Yp8fFHQoix-0eMthwHtc6",
//   },
//   is_cancelled: false,
//   currentStep: null,
// };

export const useTaskStore = create<TaskState>()(
  devtools(
    persist(
      (set, get) => {
        const _monitorTaskProgress = async (
          taskIdToMonitor: string,
          token: string,
          isRetrieval: boolean = false
        ) => {
          const abortController = new AbortController();
          set({
            abortController,
            currentStep: "processing",
            progress: isRetrieval ? 15 : 10,
            is_cancelled: false,
          });

          if (isRetrieval) {
            useAppStore.setState({
              pageMode: "Waiting",
              buttonLoading: true,
            });
          } else {
            useAppStore.setState({
              pageMode: "Waiting",
              errorContent: "",
              errorTitle: "",
              buttonLoading: false,
            });
          }

          const creditClient = new CreditClient({
            token: token,
            baseUrl: api.baseUrl,
          });

          try {
            const statusStream: AsyncGenerator<TaskStatus> =
              getTaskStatusStream({
                taskId: taskIdToMonitor,
                app: "vu",
                source: "canva",
                signal: abortController.signal,
                token: token,
                baseUrl: api.baseUrl,
              });

            for await (const status of statusStream) {
              if (get().is_cancelled) {
                console.log(
                  `Task monitoring cancelled by user${isRetrieval ? " during retrieve" : ""}.`
                );
                break;
              }

              if (abortController.signal.aborted) {
                console.log(
                  `Task monitoring aborted${isRetrieval ? " during retrieve" : ""}.`
                );
                break;
              }

              if (
                status.status === "PROCESSING" ||
                status.status === "INIT" ||
                status.status === "WAITING"
              ) {
                set((state) => ({
                  progress: Math.min(90, state.progress + 5),
                }));
              } else if (
                status.status === "UNKNOWN" ||
                status.status === "FAILED" ||
                status.status === "ERROR"
              ) {
                useAppStore.setState({
                  errorContent: "Please try again wait a moment.",
                  errorTitle: "Sorry, this video upscaler failed.",
                  pageMode: "Generate",
                  buttonLoading: false,
                });
                set({
                  currentStep: null,
                  progress: 0,
                  taskId: "",
                  abortController: null,
                });
                break;
              } else if (status.status === "FINISHED") {
                set({
                  progress: 100,
                  taskResult: {
                    thumbUrl: status.thumbUrl,
                    videoUrl: status.videoUrl,
                    taskId: status.taskId,
                  },
                  currentStep: null,
                });
                useAppStore.setState({
                  pageMode: "Result",
                  buttonLoading: false,
                });

                const resp: CreditResponse = await creditClient.getUserCredit();
                useAuthStore.getState().updateCredit(resp.data);
                abortController.abort();
                break;
              } else {
                console.error(`Unexpected task status: ${status}`);
                useAppStore.setState({
                  errorContent: "An unexpected error occurred.",
                  errorTitle: "Error",
                  pageMode: "Generate",
                  buttonLoading: false,
                });
                set({
                  currentStep: null,
                  progress: 0,
                  taskId: "",
                  abortController: null,
                });
                break;
              }
            }
          } catch (err: any) {
            console.error(
              `Error ${isRetrieval ? "retrieving" : "monitoring"} task status (${taskIdToMonitor}):`,
              err
            );
            // Only update UI if not an AbortError or explicitly cancelled
            if (err.name !== "AbortError" && !get().is_cancelled) {
              useAppStore.setState({
                errorContent:
                  err.message ||
                  `Failed to ${isRetrieval ? "retrieve" : "monitor"} task status.`,
                errorTitle: "Network Error",
                pageMode: "Generate",
                buttonLoading: false,
              });
              set({
                currentStep: null,
                progress: 0,
                // taskId: "",
                abortController: null,
              });
            }
          } finally {
            if (
              get().abortController === abortController &&
              !abortController.signal.aborted
            ) {
              abortController.abort(); // Ensure it's aborted if loop finished unexpectedly
            }
            if (get().abortController === abortController) {
              set({ abortController: null }); // Clear our specific abort controller
            }
          }
        };

        return {
          ...defaultTaskState,

          resetState: () => {
            console.log("Resetting state");
            const currentAbortController = get().abortController;
            if (currentAbortController) {
              currentAbortController.abort();
            }
            set({ ...defaultTaskState });
            useAppStore.getState().resetAppState();
          },

          handleCancel: async () => {
            const { a1dToken } = useAuthStore.getState();
            const { taskId, abortController } = get();
            if (!taskId || !a1dToken) {
              console.error("No task ID or token available for cancellation.");
              return;
            }

            // 立即设置取消状态，显示加载状态
            set({ is_cancelled: true, isCancelling: true });
            if (abortController) {
              abortController.abort();
            }

            try {
              await cancelTask(api.baseUrl, taskId, a1dToken);
              console.log(`Task ${taskId} cancellation requested.`);
            } catch (error) {
              console.error(
                `Failed to request cancellation for task ${taskId}:`,
                error
              );
            } finally {
              get().resetState();
            }
          },

          handleVuGenerate: async (data: VuGenerateData) => {
            const { a1dToken, login } = useAuthStore.getState();

            if (!a1dToken) {
              await login();
              return;
            }

            const currentToken = a1dToken;

            if (get().abortController) {
              get().abortController?.abort();
              set({ abortController: null });
            }

            set({
              progress: 5,
              taskResult: null,
              is_cancelled: false,
              currentStep: null,
            });

            try {
              set({ progress: 10 });

              useAppStore.setState({
                pageMode: "Waiting",
                errorContent: "",
                errorTitle: "",
                buttonLoading: true,
              });
              set({ is_cancelled: false });

              let videoUrlToProcess = data.videoUrl || "";
              console.log("videoUrlToProcess", videoUrlToProcess);

              // 如果没有 videoUrl，则上传 videoSrc
              if (!videoUrlToProcess && data.videoSrc) {
                set({ currentStep: "uploading" });
                const response = await api.uploadBase64File({
                  base64Data: data.videoSrc,
                  fileName: `vu-${Date.now()}.${data.videoMimeType.split("/")[1]}`,
                  app: "vu",
                  mimeType: data.videoMimeType,
                  token: currentToken,
                  baseUrl: api.baseUrl,
                });
                videoUrlToProcess = response.url;
              }

              if (!videoUrlToProcess) {
                throw new Error("No video source available for processing.");
              }

              // 保存 videoUrlToProcess 到 store 中
              set({ videoUrlToProcess });

              set({ currentStep: "submitting" });
              const taskResponse = await api.createVideoUpScalerTask({
                videoUrl: videoUrlToProcess,
                enableUpscale: data.enableUpscale,
                videoQuality: data.videoQuality,
                mimeType: data.videoMimeType,
                model: data.model,
                source: "canva",
                token: currentToken,
                baseUrl: api.baseUrl,
              });

              set({
                taskId: taskResponse.taskId,
              });

              await _monitorTaskProgress(
                taskResponse.taskId,
                currentToken,
                false
              );
            } catch (err: any) {
              console.error("Error in handleVuGenerate:", err);
              set({
                currentStep: null,
                progress: 0,
                // taskId: "",
                abortController: null,
              });
              useAppStore.setState({
                errorContent:
                  err.message || "An error occurred during task generation.",
                errorTitle: "Generation Error",
                pageMode: "Generate",
                buttonLoading: false,
              });
            } finally {
              if (get().currentStep !== "processing") {
                useAppStore.setState({ buttonLoading: false });
              }
            }
          },

          retrieveProcessingTaskStatus: async () => {
            const {
              taskId,
              taskResult,
              is_cancelled: isTaskCancelledStore,
            } = get();
            const { a1dToken, login } = useAuthStore.getState();

            if (taskId && !taskResult && !isTaskCancelledStore) {
              console.log(
                `Found persisted task ID: ${taskId}. Attempting to retrieve status.`
              );

              let currentToken = a1dToken;
              if (!currentToken) {
                await login();
                return;
              }

              if (get().abortController) {
                console.log(
                  "Aborting existing monitoring before starting retrieval."
                );
                get().abortController?.abort();
                set({ abortController: null });
              }

              useAppStore.setState({
                pageMode: "Waiting",
                buttonLoading: true,
              });
              set({
                currentStep: "processing",
                progress: 15,
                is_cancelled: false,
              });

              await _monitorTaskProgress(taskId, currentToken, true);
            } else if (taskId && taskResult) {
              console.log(
                `Found persisted completed task ID: ${taskId}. Restoring result page.`
              );
              useAppStore.setState({
                pageMode: "Result",
                buttonLoading: false,
              });
              set({ currentStep: null });
            } else if (isTaskCancelledStore) {
              console.log(
                `Task ${taskId} was previously cancelled. Resetting state.`
              );
              get().resetState();
            }
          },
        };
      },
      {
        name: "vu-task-storage",
        storage: createJSONStorage(() => storageWithExpiration),
        partialize: (state) => ({
          taskId: state.taskId,
          taskResult: state.taskResult,
          videoUrlToProcess: state.videoUrlToProcess,
        }),
        onRehydrateStorage: () => {
          console.log("Hydration preparing for taskStore");
          return (state, error) => {
            if (error) {
              console.error(
                "An error occurred during taskStore hydration:",
                error
              );
            } else {
              console.log("Hydration finished for taskStore:", state);
              const { a1dToken } = useAuthStore.getState();
              if (!a1dToken) {
                console.log("No user login status during hydration");
                return;
              }
              if (state?.taskId && !state?.taskResult) {
                console.log(`Task ${state.taskId} may need status retrieval.`);
              } else if (state?.taskId && state?.taskResult) {
                console.log(
                  `Task ${state.taskId} is already completed. Restoring result page on hydration.`
                );
                useAppStore.setState({ pageMode: "Result" });
              }
            }
          };
        },
      }
    ),
    { name: "vuTaskStore" }
  )
);
