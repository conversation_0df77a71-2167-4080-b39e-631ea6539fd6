import { create } from "zustand";

interface VuGenerateSettingState {
  videoUrl: string;
  videoQuality: string;
  enableUpscale: boolean;
  model: string;
  duration: number;
  // actions
  setVideoUrl: (value: string) => void;
  setVideoQuality: (value: string) => void;
  setEnableUpscale: (value: boolean) => void;
  setModel: (value: string) => void;
  setDuration: (value: number) => void;
  resetGenerateSetting: () => void;
}

const initialVuGenerateSetting = {
  videoUrl: "",
  videoQuality: "720P",
  enableUpscale: true,
  model: "general",
  duration: 0,
};

export const useVuGenerateSettingStore = create<VuGenerateSettingState>(
  (set) => ({
    ...initialVuGenerateSetting,

    setVideoUrl: (value: string) => {
      set({ videoUrl: value });
    },

    setVideoQuality: (value: string) => {
      set({ videoQuality: value });
    },

    setEnableUpscale: (value: boolean) => {
      set({ enableUpscale: value });
    },

    setModel: (value: string) => {
      set({ model: value });
    },

    setDuration: (value: number) => {
      set({ duration: value });
    },

    resetGenerateSetting: () => {
      set({ ...initialVuGenerateSetting });
    },
  })
);
