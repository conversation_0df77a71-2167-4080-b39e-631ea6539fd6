import { create } from "zustand";
import { Tone } from "@canva/app-ui-kit/dist/cjs/ui/apps/developing/ui_kit/components/alert/alert";
import { VideoInfo } from "../types/generate";

export type UploadTip = {
  title: string;
  desc: string;
  tone: Tone;
};

export type VideoFromType = "upload" | "selection" | null;
export type VideoSize = {
  width: number;
  height: number;
};

export type VideoStore = {
  videoSrc: string | null;
  videoUrl: string | null;
  videoLoading: boolean;
  videoFromType: VideoFromType;
  fileName: string;
  videoMimeType: string;
  thumbnailUrl: string;
  uploadTip: UploadTip;
  duration: number;
  resolution: string;
  videoSize: VideoSize;
  setVideoSrc: (src: string | null) => void;
  setVideoLoading: (loading: boolean) => void;
  setVideoFromType: (type: VideoFromType) => void;
  setFileName: (name: string) => void;
  setVideoMimeType: (type: string) => void;
  setUploadTip: (tip: UploadTip) => void;
  resetVideoStore: () => void;
  setVideoInfo: (info: VideoInfo) => void;
  setDuration: (duration: number) => void;
  setResolution: (resolution: string) => void;
  setThumbnailUrl: (url: string | null) => void;
  setVideoSize: (size: VideoSize) => void;
  setVideoUrl: (url: string | null) => void;
  hideUploadTip: () => void;
};

export const useVideoStore = create<VideoStore>((set) => ({
  videoSrc: null,
  videoLoading: false,
  videoFromType: null,
  fileName: "",
  videoMimeType: "",
  duration: 0,
  resolution: "360p",
  thumbnailUrl: "",
  videoUrl: null,
  uploadTip: {
    title: "",
    desc: "",
    tone: "critical",
  },
  videoSize: {
    width: 0,
    height: 0,
  },
  setVideoSrc: (src) => set({ videoSrc: src }),
  setVideoLoading: (loading) => set({ videoLoading: loading }),
  setVideoFromType: (type) => set({ videoFromType: type }),
  setFileName: (name) => set({ fileName: name }),
  setVideoMimeType: (type) => set({ videoMimeType: type }),
  setUploadTip: (tip) => set({ uploadTip: tip }),
  setDuration: (duration) => set({ duration }),
  setResolution: (resolution) => set({ resolution }),
  resetVideoStore: () =>
    set({
      videoSrc: null,
      videoLoading: false,
      videoFromType: null,
      fileName: "",
      videoMimeType: "",
      uploadTip: {
        title: "",
        desc: "",
        tone: "critical",
      },
      duration: 0,
      thumbnailUrl: "",
      resolution: "360p",
      videoUrl: null,
    }),
  setVideoInfo: (info: VideoInfo) =>
    set({
      videoSrc: info.src,
      fileName: info.name,
      videoMimeType: info.type,
      videoFromType: "upload",
      duration: info.duration,
      thumbnailUrl: info.thumbnailUrl,
      resolution: info.resolution || "360p",
      videoUrl: info.url || null,
    }),
  setThumbnailUrl: (url) => set({ thumbnailUrl: url || "" }),
  setVideoSize: (size) => set({ videoSize: size }),
  hideUploadTip: () =>
    set({
      uploadTip: {
        title: "",
        desc: "",
        tone: "critical",
      },
    }),
  setVideoUrl: (url) => set({ videoUrl: url }),
}));
