import { But<PERSON>, Rows, Box, Text, Alert } from "@canva/app-ui-kit";
import * as React from "react";
import * as styles from "styles/result.css";
import { upload } from "@canva/asset";
import { addElementAtPoint, ui } from "@canva/design";
import { Tone } from "@canva/app-ui-kit/dist/cjs/ui/apps/developing/ui_kit/components/alert/alert";
import { useFeatureSupport } from "utils/use_feature_support";
import { FormattedMessage, useIntl } from "react-intl";
import { track } from "./utils";
import { useImageStore } from "./stores/imageStore";
import { useTaskStore } from "./stores/taskStore";
import CompareVideo from "./components/compar-video";

export default function Result() {
  const intl = useIntl();
  const isSupported = useFeatureSupport();
  const [saveVideoLoading, setSaveVideoLoading] = React.useState(false);

  const hasAddToDesign = React.useRef(false);
  const [showTip, setShowTip] = React.useState({
    title: "",
    desc: "",
    tone: "critical",
  });
  const videoUploadResultRef = React.useRef<any>(null);
  const [videoBoxHeight, setVideoBoxHeight] = React.useState(300);
  const [videoWidth, setVideoWidth] = React.useState(300);
  const imgSize = useImageStore((state) => state.imgSize);
  const taskResult = useTaskStore((state) => state.taskResult);
  const taskId = useTaskStore((state) => state.taskId);
  const resetState = useTaskStore((state) => state.resetState);
  const videoUrlToProcess = useTaskStore((state) => state.videoUrlToProcess);

  const showSuccessTip = () => {
    setShowTip({
      title: intl.formatMessage({
        defaultMessage: "Successfully added video to design",
        description: "An alert message after adding the video to design.",
      }),
      desc: "",
      tone: "positive",
    });
  };

  const saveVideo = async (event?: any) => {
    if (event) {
      event.preventDefault();
      event.stopPropagation();
    }
    setSaveVideoLoading(true);
    setShowTip({
      title: "",
      desc: "",
      tone: "critical",
    });
    try {
      if (videoUploadResultRef.current) {
        if (!isSupported(addElementAtPoint)) {
          return;
        }

        await addElementAtPoint({
          altText: {
            text: "video",
            decorative: false,
          },
          type: "video",
          ref: videoUploadResultRef.current,
        });

        showSuccessTip();
        track("AddVideoToDesign", taskId);
        hasAddToDesign.current = true;
      } else {
        // Start uploading the file
        console.log("lwq - taskResult", taskResult);
        const video = await upload({
          type: "video",
          mimeType: "video/mp4",
          url: taskResult?.videoUrl ?? "",
          thumbnailImageUrl: taskResult?.thumbUrl ?? "",
          aiDisclosure: "app_generated",
        });

        // Get a reference to the asset in Canva's backend
        // console.log("The asset reference is", video.ref);
        // Wait for the upload to complete
        await video.whenUploaded();
        videoUploadResultRef.current = video.ref;

        if (!isSupported(addElementAtPoint)) {
          return;
        }
        await addElementAtPoint({
          altText: {
            text: "video",
            decorative: false,
          },
          type: "video",
          ref: video.ref,
        });

        showSuccessTip();
        track("AddVideoToDesign", taskId);
        hasAddToDesign.current = true;
      }
    } catch (error) {
      console.error(error);
      if (event) {
        setShowTip({
          title: intl.formatMessage({
            defaultMessage: "An error occurred.",
            description:
              "An error message when an error occurs with no specific message.",
          }),
          desc: intl.formatMessage({
            defaultMessage: "please try again.",
            description:
              "An error message when an error occurs with no specific message.",
          }),
          tone: "critical",
        });
        track("AddVideoToDesignError", taskId, JSON.stringify(error));
      }
    } finally {
      setSaveVideoLoading(false);
    }
  };

  const tipHide = () => {
    setShowTip({
      title: "",
      desc: "",
      tone: "critical",
    });
  };

  const clickback = () => {
    tipHide();
    videoUploadResultRef.current = null;
    setSaveVideoLoading(false);
    resetState();
  };

  React.useEffect(() => {
    track("ResultPageVisit", taskId);

    const videoBox = document.getElementById("videoBox");
    if (videoBox) {
      const width = videoBox.clientWidth;

      let height = (width / imgSize.width) * imgSize.height;
      if (height > 336) {
        height = 336;
      }
      const realW = (height / imgSize.height) * imgSize.width;
      console.log("lwq - realW", realW, height);
      setVideoBoxHeight(height);
      setVideoWidth(realW);
    }
  }, []);

  return (
    <Rows spacing="1u">
      {showTip.title ? (
        <Box>
          <Alert
            tone={showTip.tone as Tone}
            title={showTip.title}
            onDismiss={tipHide}
          >
            {showTip.desc}
          </Alert>
        </Box>
      ) : (
        <></>
      )}

      <div>
        <Text size="medium" variant="bold" tone="primary">
          <FormattedMessage
            defaultMessage="Your video"
            description="Your video"
          />
        </Text>
      </div>
      <Box
        borderRadius="standard"
        background="neutral"
        className={styles.videoBox}
      >
        <div id="videoBox">
          <Box width="full" height="full" borderRadius="standard">
            {videoUrlToProcess ? (
              <CompareVideo
                oldVideoSrc={videoUrlToProcess ?? ""}
                newVideoUrl={taskResult?.videoUrl ?? ""}
              />
            ) : (
              <video
                src={taskResult?.videoUrl ?? ""}
                controls
                autoPlay
                loop
                muted
                style={{ width: "100%", height: "100%", display: "block" }}
              />
            )}
          </Box>
        </div>
      </Box>
      <Box paddingTop="1u">
        <Button
          variant="primary"
          stretch
          onClick={saveVideo}
          loading={saveVideoLoading}
        >
          {intl.formatMessage({
            defaultMessage: "Add to design",
            description: "Add to design",
          })}
        </Button>
      </Box>
      <Box paddingBottom="2u">
        <Button variant="secondary" stretch onClick={clickback}>
          {intl.formatMessage({
            defaultMessage: "Go back",
            description:
              "Text for a button, It means to go back to the homepage.",
          })}
        </Button>
      </Box>
    </Rows>
  );
}
