import { Tone } from "@canva/app-ui-kit/dist/cjs/ui/apps/developing/ui_kit/components/alert/alert";

export type ImageFromType = "selection" | "selection-base64" | "upload" | "";

export interface ImageSize {
  width: number;
  height: number;
}

export interface UploadTip {
  title: string;
  desc: string;
  tone: Tone;
}

export interface ImageInfo {
  imgSrc: string | null;
  imgSize: ImageSize;
  fromType: ImageFromType;
  imgMimeType: string;
}

export type GenerateData = {
  sketchDuration: number;
  colorFillDuration: number;
  isFillWithColor: boolean;
  needHand: boolean;
  imgUrl: string;
  imgSrc: string | null;
  imgMimeType: string;
};

export type VuGenerateData = {
  enableUpscale: boolean;
  videoQuality: string;
  videoUrl: string; // updated video url
  videoSrc: string | null; // local video src
  videoMimeType: string;
  model: string;
};

export type TaskResult = {
  thumbUrl: string;
  videoUrl: string;
  taskId: string;
};

export type PageMode = "Generate" | "Waiting" | "Result" | "Upload";

export type VideoInfo = {
  src?: string | null;
  name: string;
  type: string;
  size: number;
  duration: number;
  resolution?: string;
  thumbnailUrl?: string;
  url?: string | null;
};
