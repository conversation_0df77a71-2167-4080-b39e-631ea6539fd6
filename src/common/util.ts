export const formatFileSize = (bytes: number) => {
  return (bytes / (1024 * 1024)).toFixed(2) + " MB";
};

export const formatDuration = (seconds: number) => {
  const roundedUp = Math.ceil(seconds * 10) / 10;
  return roundedUp.toFixed(1);
};

export const getVideoResolution = (videoElement: HTMLVideoElement): string => {
  const height = videoElement.videoHeight;
  if (height >= 2160) return "2160p";
  if (height >= 1440) return "1440p";
  if (height >= 1080) return "1080p";
  if (height >= 720) return "720p";
  if (height >= 480) return "480p";
  if (height >= 360) return "360p";
  if (height >= 240) return "240p";
  return `${height}p`;
};

// Function to generate thumbnailUrl from video
export const generateThumbnailUrl = async (
  videoUrl: string
): Promise<string> => {
  return new Promise((resolve) => {
    const video = document.createElement("video");
    video.src = videoUrl;
    video.crossOrigin = "anonymous";
    video.muted = true;
    video.playsInline = true;

    video.addEventListener("loadedmetadata", () => {
      video.currentTime = video.duration * 0.25;
    });

    video.addEventListener("seeked", () => {
      const canvas = document.createElement("canvas");
      canvas.width = video.videoWidth;
      canvas.height = video.videoHeight;

      const ctx = canvas.getContext("2d");
      if (ctx) {
        ctx.drawImage(video, 0, 0, canvas.width, canvas.height);

        // Convert canvas to data URL
        const thumbnailUrl = canvas.toDataURL("image/jpeg", 0.8);
        URL.revokeObjectURL(videoUrl);

        resolve(thumbnailUrl);
      } else {
        resolve("");
      }
    });

    // Handle errors
    video.addEventListener("error", () => {
      console.error("Error generating thumbnail");
      URL.revokeObjectURL(videoUrl);
      resolve("");
    });

    video.load();
  });
};
