import sha256 from 'sha256';

export function getUuid() {
  return new Date().getTime().toString(16) + Math.random().toString(16).substring(2);
}

// 加载远程js文件
export function loadScript(url: string, name: string, callback: () => void) {
  var script = document.createElement('script') as HTMLScriptElement;
  script.id = name
  script.type = 'text/javascript'
  script.src = url
  script.onload = function () {
    callback()
  }
  document.body.appendChild(script)
}

 export const commonFetch = (api: string, params: any = {}, method: string = "POST") => {
  return new Promise<any>(async (resolve, reject) => {
    try {
      const fetchData = await fetch(
        api,
        {
          method: method,
          headers: {
            "Content-Type": "Content-Type: application/json",
          },
          mode: "cors",
          body: JSON.stringify({
            ...params,
          })
        }
      );
      // console.log("++++++++++++fetchData", fetchData);
      if (!fetchData.ok) {
        reject(fetchData);
      }
      const res: any = await fetchData.json();
      resolve(res);
    } catch (error) {
      reject(error);
    }
  });
}

export const initWebSocket = (wsLink: string, handleWsMessage: (msg: string) => void, callback: (ws: WebSocket) => void) => {
  const ws = new WebSocket(wsLink);

  ws.onopen = () => {
    console.log('WebSocket connected');
    callback && callback(ws);
  };

  ws.onmessage = (event) => {
    const receivedMessage = event.data;
    // console.log('Received message:', JSON.parse(receivedMessage));
    handleWsMessage(receivedMessage);
  };

  ws.onerror = (error) => {
    console.error('WebSocket error:', error);
  };

  ws.onclose = () => {
    console.log('WebSocket closed');
  };
}

export const createXSign = () =>{
  // 直接获取当前UTC时间的毫秒数
  const utcMilliSeconds = new Date().getTime();
  // 使用UTC时间戳生成签名
  const sign = sha256(Math.floor(utcMilliSeconds * 1.01).toString()).toUpperCase().substring(0,32);
  // 返回UTC时间戳和签名的组合
  return utcMilliSeconds.toString() + sign;
}

/**
 * 埋点
 * trackId：埋点id
 * scenes：场景，项目
 * type：埋点key
 * extraInfo：扩展信息
 */
export const track = ({trackId, scenes, type, extraInfo = ""}) => {
  const xSign = createXSign();
  fetch(
    `https://dj7jvjeb1v6pc.cloudfront.net/prod/user-behavior-record/record`,
    {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      mode: "cors",
      body: JSON.stringify({
        operation: "user-behavior-record",
        sign: xSign,
        payload: {
          id: trackId,
          eventType: type,
          scenes: scenes,
          extraInfo: extraInfo,
        },
      })
    }
  );
}

export const uploadImageToS3 = async (imageBase64: string, imageType: string) => {
  try {
    const fetchData = await fetch(
      `https://d2eakv7kouninq.cloudfront.net/prod/upload-file`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        mode: "cors",
        body: JSON.stringify({
          id: getUuid(),
          app: "a1d-speed-painter",
          fileBase64: imageBase64,
          fileType: imageType,
          sign: createXSign(),
        })
      }
    );
    if (fetchData.ok) {
      const res = await fetchData.json();
      if (res.fileUrl) {
        if (res.fileUrl.body) {
          return false;
        }
        return  res.fileUrl;
      }
    } else {
      return false;
    }
  } catch (error) {
    return false;
  }
}

