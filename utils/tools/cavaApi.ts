import { addNativeElement } from "@canva/design";
import { upload, ImageMimeType, QueuedImage } from "@canva/asset";
import { getUuid } from "./common";

// 将图片上传到 canva 图库
export const uploadImageToCanvaAssets = async (mimeType: ImageMimeType, imgUrl: string, thumbnailUrl: string) => {
  const uploadParams: any = {
    type: "IMAGE",
    id: getUuid(),
    mimeType: mimeType,
    url: imgUrl,
    thumbnailUrl: thumbnailUrl,
  }
  const result = await upload(uploadParams);
  return result;
}

export const addNativeElementToCanva = async (result: QueuedImage) => {
  // 添加新图片到画板
  await addNativeElement({
    type: "IMAGE",
    ref: result.ref,
  });
}