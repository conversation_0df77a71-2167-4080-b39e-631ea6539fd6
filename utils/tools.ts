export const imageToBase64  = (img, imageType) => {
  const w = img.width;
  const h = img.height;
  // console.log(w, h);
  const canvas = document.createElement("canvas");
  const max = 1000 * 1000 * 3;
  const scale = max / (w * h);
  // let width = w > 800 ? 800 : w / 2; // 960 * 720
  // let height = (width / w) * h;
  // if (width * height > 960 * 720) {
  //   width = width > 600 ? 600 : width / 1.5; // 960 * 720
  //   height = (width / w) * h;

  //   if (width * height > 960 * 720) {
  //     width = width > 500 ? 500 : width / 1.5; // 960 * 720
  //     height = (width / w) * h;
  //   }
  // }
  const width = w * scale;
  const height = h * scale;
  canvas.width = width;
  canvas.height = height;
  const ctx = canvas.getContext("2d");
  // console.log("+++++++++++ctx", ctx);
  if (ctx) {
    ctx.drawImage(img, 0, 0, width, height);
    const imgBase64 = canvas.toDataURL(imageType, 1);
    if (imgBase64.length > 1000 * 1000 * 2) {
      return imageToBase64_2(img, imageType, (1000 * 1000 * 2) / imgBase64.length);
    } else {
      return {
        imgBase64,
        width: width,
        height: height,
      };
    }
  }
  return {
    imgBase64: "",
    width: width,
    height: height,
  };
}

const imageToBase64_2 = (img, imageType, scale) => {
  const w = img.width;
  const h = img.height;
  const canvas = document.createElement("canvas");
  const width = w * scale;
  const height = h * scale;
  canvas.width = width;
  canvas.height = height;
  const ctx = canvas.getContext("2d");
  // console.log("+++++++++++ctx", ctx);
  if (ctx) {
    ctx.drawImage(img, 0, 0, width, height);
    const imgBase64 = canvas.toDataURL(imageType, 1);
    return {
      imgBase64,
      width: width,
      height: height,
    };
  }
  return {
    imgBase64: "",
    width: width,
    height: height,
  };
}